@echo off
chcp 65001 >nul
title بناء البرنامج - Network Manager Pro

echo.
echo 🚀 بناء مدير الشبكة المتقدم
echo ================================
echo.

:: التأكد من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 حمل Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python موجود
python --version
echo.

:: تثبيت المتطلبات الأساسية
echo 📦 تثبيت المتطلبات...
pip install --upgrade pip
pip install PyQt5==5.15.9
pip install scapy==2.5.0
pip install psutil==5.9.5
pip install netifaces==0.11.0
pip install pyinstaller
pip install requests

echo.
echo ✅ تم تثبيت المتطلبات
echo.

:: بناء ملف EXE
echo 🔨 بناء ملف EXE...
pyinstaller --onefile --windowed --name="NetworkManagerPro" --uac-admin gui_main.py

if errorlevel 1 (
    echo ❌ فشل في بناء EXE
    pause
    exit /b 1
)

echo ✅ تم بناء EXE بنجاح
echo.

:: إنشاء مجلد التوزيع
echo 📁 إنشاء مجلد التوزيع...
if exist "NetworkManagerPro_Ready" rmdir /s /q "NetworkManagerPro_Ready"
mkdir "NetworkManagerPro_Ready"

:: نسخ ملف EXE
copy "dist\NetworkManagerPro.exe" "NetworkManagerPro_Ready\"

:: إنشاء ملف التشغيل
echo @echo off > "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo chcp 65001 ^>nul >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo title مدير الشبكة المتقدم >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo. >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo echo 🚀 مدير الشبكة المتقدم >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo echo ======================= >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo echo. >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo echo 🔐 تشغيل البرنامج بصلاحيات المدير... >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo powershell -Command "Start-Process '%~dp0NetworkManagerPro.exe' -Verb RunAs" >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo echo. >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo echo ✅ تم تشغيل البرنامج >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"
echo pause >> "NetworkManagerPro_Ready\تشغيل_البرنامج.bat"

:: إنشاء ملف README
echo # مدير الشبكة المتقدم - Network Manager Pro > "NetworkManagerPro_Ready\README.txt"
echo. >> "NetworkManagerPro_Ready\README.txt"
echo ## طريقة التشغيل: >> "NetworkManagerPro_Ready\README.txt"
echo 1. اضغط دبل كليك على "تشغيل_البرنامج.bat" >> "NetworkManagerPro_Ready\README.txt"
echo 2. أو اضغط يمين على "NetworkManagerPro.exe" واختر "Run as administrator" >> "NetworkManagerPro_Ready\README.txt"
echo. >> "NetworkManagerPro_Ready\README.txt"
echo ## متطلبات مهمة: >> "NetworkManagerPro_Ready\README.txt"
echo - يجب تثبيت Npcap من: https://nmap.org/npcap/ >> "NetworkManagerPro_Ready\README.txt"
echo - تأكد من تحديد خيار "Install Npcap in WinPcap API-compatible Mode" >> "NetworkManagerPro_Ready\README.txt"
echo - البرنامج يحتاج صلاحيات المدير للعمل >> "NetworkManagerPro_Ready\README.txt"
echo. >> "NetworkManagerPro_Ready\README.txt"
echo ## الميزات: >> "NetworkManagerPro_Ready\README.txt"
echo - فحص الشبكة واكتشاف الأجهزة >> "NetworkManagerPro_Ready\README.txt"
echo - حجب الأجهزة من الإنترنت >> "NetworkManagerPro_Ready\README.txt"
echo - التحكم في سرعة الإنترنت >> "NetworkManagerPro_Ready\README.txt"
echo - حفظ الإعدادات تلقائياً >> "NetworkManagerPro_Ready\README.txt"
echo - واجهة عربية سهلة الاستخدام >> "NetworkManagerPro_Ready\README.txt"

:: إنشاء مجلدات فرعية
mkdir "NetworkManagerPro_Ready\logs"
mkdir "NetworkManagerPro_Ready\backups"

echo.
echo 🎉 تم إنشاء البرنامج بنجاح!
echo.
echo 📁 البرنامج موجود في مجلد: NetworkManagerPro_Ready
echo 📋 اقرأ ملف README.txt للتعليمات
echo ⚠️ لا تنس تثبيت Npcap من: https://nmap.org/npcap/
echo.

:: فتح مجلد النتائج
start "" "NetworkManagerPro_Ready"

echo ✅ تم فتح مجلد البرنامج
pause
