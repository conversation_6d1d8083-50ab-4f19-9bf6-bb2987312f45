"""
الواجهة الرسومية الرئيسية - إدارة الشبكة والأجهزة
Main GUI - Network and Device Management
"""

import sys
import os
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel, QLineEdit,
    QComboBox, QSpinBox, QTextEdit, QTabWidget, QGroupBox,
    QMessageBox, QProgressBar, QCheckBox, QSplitter, QHeaderView,
    QDialog, QDialogButtonBox, QFormLayout, QStatusBar
)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt
from PyQt5.QtGui import QIcon, QFont, QPixmap

# استيراد الوحدات المحلية
from database import DatabaseManager
from network_scanner import NetworkScanner
from arp_spoofer import ARPSpoofer
from bandwidth_controller import BandwidthController
from logger import NetworkLogger

class DeviceTableWidget(QTableWidget):
    """جدول مخصص لعرض الأجهزة مع ميزات إضافية"""
    
    def __init__(self):
        super().__init__()
        self.setup_table()
    
    def setup_table(self):
        """إعداد الجدول وأعمدته"""
        # تحديد الأعمدة
        headers = [
            "IP Address", "MAC Address", "اسم الجهاز", "الحالة", 
            "الإجراء", "حد التحميل", "حد الرفع", "آخر ظهور"
        ]
        
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        
        # تعديل عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        # إخفاء أرقام الصفوف
        self.verticalHeader().setVisible(False)

class NetworkScanThread(QThread):
    """Thread منفصل لفحص الشبكة"""
    devices_found = pyqtSignal(list)
    scan_finished = pyqtSignal()
    
    def __init__(self, scanner: NetworkScanner):
        super().__init__()
        self.scanner = scanner
        self.running = True
    
    def run(self):
        """تشغيل فحص الشبكة"""
        while self.running:
            try:
                devices = self.scanner.scan_network_arp()
                self.devices_found.emit(devices)
                time.sleep(30)  # فحص كل 30 ثانية
            except Exception as e:
                print(f"❌ خطأ في thread فحص الشبكة: {e}")
                time.sleep(5)
    
    def stop(self):
        """إيقاف الفحص"""
        self.running = False

class DeviceSettingsDialog(QDialog):
    """نافذة إعدادات الجهاز"""
    
    def __init__(self, device_info: Dict, parent=None):
        super().__init__(parent)
        self.device_info = device_info
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة نافذة الإعدادات"""
        self.setWindowTitle(f"إعدادات الجهاز - {self.device_info.get('ip', 'Unknown')}")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout()
        
        # معلومات الجهاز
        info_group = QGroupBox("معلومات الجهاز")
        info_layout = QFormLayout()
        
        info_layout.addRow("IP Address:", QLabel(self.device_info.get('ip', 'Unknown')))
        info_layout.addRow("MAC Address:", QLabel(self.device_info.get('mac', 'Unknown')))
        
        self.hostname_edit = QLineEdit(self.device_info.get('hostname', ''))
        info_layout.addRow("اسم الجهاز:", self.hostname_edit)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # إعدادات التحكم
        control_group = QGroupBox("إعدادات التحكم")
        control_layout = QFormLayout()
        
        self.action_combo = QComboBox()
        self.action_combo.addItems(["السماح", "حجب", "تحديد السرعة"])
        control_layout.addRow("الإجراء:", self.action_combo)
        
        self.download_limit = QSpinBox()
        self.download_limit.setRange(0, 10000)
        self.download_limit.setSuffix(" KB/s")
        control_layout.addRow("حد التحميل:", self.download_limit)
        
        self.upload_limit = QSpinBox()
        self.upload_limit.setRange(0, 10000)
        self.upload_limit.setSuffix(" KB/s")
        control_layout.addRow("حد الرفع:", self.upload_limit)
        
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)
        
        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
    
    def get_settings(self) -> Dict:
        """الحصول على الإعدادات المدخلة"""
        action_map = {
            "السماح": "allow",
            "حجب": "block", 
            "تحديد السرعة": "limit"
        }
        
        return {
            'hostname': self.hostname_edit.text(),
            'action': action_map[self.action_combo.currentText()],
            'download_limit': self.download_limit.value(),
            'upload_limit': self.upload_limit.value()
        }

class MainWindow(QMainWindow):
    """النافذة الرئيسية للبرنامج"""
    
    def __init__(self):
        super().__init__()
        
        # تهيئة المكونات الأساسية
        self.db_manager = DatabaseManager()
        self.network_scanner = NetworkScanner()
        self.arp_spoofer = None
        self.bandwidth_controller = BandwidthController()
        self.logger = NetworkLogger()
        
        # متغيرات الحالة
        self.current_devices = []
        self.scan_thread = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        
        # بدء فحص الشبكة
        self.start_network_scanning()
        
        # تحميل الإعدادات المحفوظة
        self.load_saved_settings()
        
        self.logger.log_info("تم بدء تشغيل البرنامج", "security")
    
    def setup_ui(self):
        """إعداد الواجهة الرسومية"""
        self.setWindowTitle("مدير الشبكة المتقدم - Network Manager Pro")
        self.setGeometry(100, 100, 1200, 800)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # شريط الأدوات العلوي
        self.create_toolbar(main_layout)
        
        # التبويبات الرئيسية
        self.create_tabs(main_layout)
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات العلوي"""
        toolbar_layout = QHBoxLayout()
        
        # أزرار التحكم الرئيسية
        self.scan_btn = QPushButton("🔍 فحص الشبكة")
        self.scan_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; }")
        
        self.block_all_btn = QPushButton("🚫 حجب الكل")
        self.block_all_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px; }")
        
        self.unblock_all_btn = QPushButton("✅ إلغاء حجب الكل")
        self.unblock_all_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px; }")
        
        self.refresh_btn = QPushButton("🔄 تحديث")
        
        # إضافة الأزرار
        toolbar_layout.addWidget(self.scan_btn)
        toolbar_layout.addWidget(self.block_all_btn)
        toolbar_layout.addWidget(self.unblock_all_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addStretch()
        
        # معلومات الشبكة
        network_info = QLabel(f"🌐 Gateway: {self.network_scanner.gateway_ip} | Local IP: {self.network_scanner.local_ip}")
        toolbar_layout.addWidget(network_info)
        
        parent_layout.addLayout(toolbar_layout)
    
    def create_tabs(self, parent_layout):
        """إنشاء التبويبات الرئيسية"""
        self.tabs = QTabWidget()
        
        # تبويب الأجهزة
        self.create_devices_tab()
        
        # تبويب السجلات
        self.create_logs_tab()
        
        # تبويب الإعدادات
        self.create_settings_tab()
        
        parent_layout.addWidget(self.tabs)
    
    def create_devices_tab(self):
        """إنشاء تبويب الأجهزة"""
        devices_widget = QWidget()
        layout = QVBoxLayout()
        
        # جدول الأجهزة
        self.devices_table = DeviceTableWidget()
        layout.addWidget(self.devices_table)
        
        # أزرار التحكم في الأجهزة
        device_controls = QHBoxLayout()
        
        self.block_device_btn = QPushButton("🚫 حجب الجهاز")
        self.unblock_device_btn = QPushButton("✅ إلغاء الحجب")
        self.limit_speed_btn = QPushButton("🚦 تحديد السرعة")
        self.device_settings_btn = QPushButton("⚙️ إعدادات الجهاز")
        
        device_controls.addWidget(self.block_device_btn)
        device_controls.addWidget(self.unblock_device_btn)
        device_controls.addWidget(self.limit_speed_btn)
        device_controls.addWidget(self.device_settings_btn)
        device_controls.addStretch()
        
        layout.addLayout(device_controls)
        
        devices_widget.setLayout(layout)
        self.tabs.addTab(devices_widget, "📱 الأجهزة")
    
    def create_logs_tab(self):
        """إنشاء تبويب السجلات"""
        logs_widget = QWidget()
        layout = QVBoxLayout()
        
        # منطقة عرض السجلات
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.logs_text)
        
        # أزرار التحكم في السجلات
        logs_controls = QHBoxLayout()
        
        self.refresh_logs_btn = QPushButton("🔄 تحديث السجلات")
        self.clear_logs_btn = QPushButton("🗑️ مسح السجلات")
        self.export_logs_btn = QPushButton("📤 تصدير السجلات")
        
        logs_controls.addWidget(self.refresh_logs_btn)
        logs_controls.addWidget(self.clear_logs_btn)
        logs_controls.addWidget(self.export_logs_btn)
        logs_controls.addStretch()
        
        layout.addLayout(logs_controls)
        
        logs_widget.setLayout(layout)
        self.tabs.addTab(logs_widget, "📋 السجلات")
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات عامة
        general_group = QGroupBox("الإعدادات العامة")
        general_layout = QFormLayout()
        
        self.auto_scan_check = QCheckBox("فحص تلقائي للشبكة")
        self.auto_scan_check.setChecked(True)
        general_layout.addRow("", self.auto_scan_check)
        
        self.scan_interval = QSpinBox()
        self.scan_interval.setRange(10, 300)
        self.scan_interval.setValue(30)
        self.scan_interval.setSuffix(" ثانية")
        general_layout.addRow("فترة الفحص:", self.scan_interval)
        
        general_group.setLayout(general_layout)
        layout.addWidget(general_group)
        
        layout.addStretch()
        settings_widget.setLayout(layout)
        self.tabs.addTab(settings_widget, "⚙️ الإعدادات")
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # عداد الأجهزة
        self.devices_count_label = QLabel("الأجهزة: 0")
        self.status_bar.addWidget(self.devices_count_label)
        
        # حالة الفحص
        self.scan_status_label = QLabel("جاهز")
        self.status_bar.addPermanentWidget(self.scan_status_label)
    
    def setup_connections(self):
        """ربط الإشارات والأحداث"""
        # أزرار شريط الأدوات
        self.scan_btn.clicked.connect(self.manual_scan)
        self.block_all_btn.clicked.connect(self.block_all_devices)
        self.unblock_all_btn.clicked.connect(self.unblock_all_devices)
        self.refresh_btn.clicked.connect(self.refresh_devices)
        
        # أزرار تحكم الأجهزة
        self.block_device_btn.clicked.connect(self.block_selected_device)
        self.unblock_device_btn.clicked.connect(self.unblock_selected_device)
        self.limit_speed_btn.clicked.connect(self.limit_selected_device_speed)
        self.device_settings_btn.clicked.connect(self.open_device_settings)
        
        # أزرار السجلات
        self.refresh_logs_btn.clicked.connect(self.refresh_logs)
        self.clear_logs_btn.clicked.connect(self.clear_logs)
        self.export_logs_btn.clicked.connect(self.export_logs)
        
        # تحديث دوري للسجلات
        self.logs_timer = QTimer()
        self.logs_timer.timeout.connect(self.refresh_logs)
        self.logs_timer.start(5000)  # كل 5 ثوان

    def start_network_scanning(self):
        """بدء فحص الشبكة في thread منفصل"""
        try:
            self.scan_thread = NetworkScanThread(self.network_scanner)
            self.scan_thread.devices_found.connect(self.update_devices_table)
            self.scan_thread.start()

            self.scan_status_label.setText("يتم الفحص...")
            self.logger.log_info("تم بدء فحص الشبكة التلقائي", "network_scan")

        except Exception as e:
            self.logger.log_error(f"خطأ في بدء فحص الشبكة: {e}", "errors")
            QMessageBox.critical(self, "خطأ", f"فشل في بدء فحص الشبكة:\n{e}")

    def update_devices_table(self, devices: List[Dict]):
        """تحديث جدول الأجهزة"""
        try:
            self.current_devices = devices
            self.devices_table.setRowCount(len(devices))

            for row, device in enumerate(devices):
                # IP Address
                self.devices_table.setItem(row, 0, QTableWidgetItem(device.get('ip', '')))

                # MAC Address
                self.devices_table.setItem(row, 1, QTableWidgetItem(device.get('mac', '')))

                # اسم الجهاز
                hostname = device.get('hostname', f"Device-{device.get('ip', '').split('.')[-1]}")
                self.devices_table.setItem(row, 2, QTableWidgetItem(hostname))

                # الحالة
                status = "متصل" if device.get('status') == 'online' else "غير متصل"
                self.devices_table.setItem(row, 3, QTableWidgetItem(status))

                # الحصول على الإعدادات المحفوظة
                saved_settings = self.db_manager.get_device_settings(device.get('mac', ''))

                if saved_settings:
                    # الإجراء
                    action_map = {'allow': 'مسموح', 'block': 'محجوب', 'limit': 'محدود السرعة'}
                    action = action_map.get(saved_settings.get('action_type', 'allow'), 'مسموح')
                    self.devices_table.setItem(row, 4, QTableWidgetItem(action))

                    # حدود السرعة
                    download_limit = saved_settings.get('download_limit', 0)
                    upload_limit = saved_settings.get('upload_limit', 0)

                    self.devices_table.setItem(row, 5, QTableWidgetItem(f"{download_limit} KB/s" if download_limit > 0 else "غير محدود"))
                    self.devices_table.setItem(row, 6, QTableWidgetItem(f"{upload_limit} KB/s" if upload_limit > 0 else "غير محدود"))

                    # تطبيق الإعدادات تلقائياً
                    self.apply_device_settings(device, saved_settings)
                else:
                    # قيم افتراضية للأجهزة الجديدة
                    self.devices_table.setItem(row, 4, QTableWidgetItem("مسموح"))
                    self.devices_table.setItem(row, 5, QTableWidgetItem("غير محدود"))
                    self.devices_table.setItem(row, 6, QTableWidgetItem("غير محدود"))

                # آخر ظهور
                last_seen = datetime.fromtimestamp(device.get('last_seen', time.time())).strftime('%H:%M:%S')
                self.devices_table.setItem(row, 7, QTableWidgetItem(last_seen))

            # تحديث عداد الأجهزة
            self.devices_count_label.setText(f"الأجهزة: {len(devices)}")
            self.scan_status_label.setText("مكتمل")

        except Exception as e:
            self.logger.log_error(f"خطأ في تحديث جدول الأجهزة: {e}", "errors")

    def apply_device_settings(self, device: Dict, settings: Dict):
        """تطبيق الإعدادات المحفوظة على الجهاز"""
        try:
            device_ip = device.get('ip')
            device_mac = device.get('mac')
            action_type = settings.get('action_type', 'allow')

            if action_type == 'block':
                # تهيئة ARP Spoofer إذا لم يكن موجوداً
                if not self.arp_spoofer:
                    self.arp_spoofer = ARPSpoofer(self.network_scanner.gateway_ip, self.network_scanner.network_interface)

                # حجب الجهاز
                success = self.arp_spoofer.block_device(device_ip, device_mac)
                if success:
                    self.logger.log_device_blocked(device_ip, device_mac)

            elif action_type == 'limit':
                # تحديد سرعة الجهاز
                download_limit = settings.get('download_limit', 0)
                upload_limit = settings.get('upload_limit', 0)

                if download_limit > 0 or upload_limit > 0:
                    success = self.bandwidth_controller.limit_device_bandwidth(device_ip, download_limit, upload_limit)
                    if success:
                        self.logger.log_bandwidth_limited(device_ip, download_limit, upload_limit)

            # تسجيل تحميل الإعدادات
            self.logger.log_device_settings_loaded(device_mac, settings)

        except Exception as e:
            self.logger.log_error(f"خطأ في تطبيق إعدادات الجهاز {device.get('ip')}: {e}", "errors")

    def get_selected_device(self) -> Optional[Dict]:
        """الحصول على الجهاز المحدد في الجدول"""
        current_row = self.devices_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_devices):
            return self.current_devices[current_row]
        return None

    def manual_scan(self):
        """فحص يدوي للشبكة"""
        try:
            self.scan_status_label.setText("يتم الفحص...")

            # فحص في thread منفصل لتجنب تجميد الواجهة
            def scan_worker():
                devices = self.network_scanner.scan_network_arp()
                self.update_devices_table(devices)

            scan_thread = threading.Thread(target=scan_worker, daemon=True)
            scan_thread.start()

            self.logger.log_info("تم بدء فحص يدوي للشبكة", "network_scan")

        except Exception as e:
            self.logger.log_error(f"خطأ في الفحص اليدوي: {e}", "errors")
            QMessageBox.critical(self, "خطأ", f"فشل في فحص الشبكة:\n{e}")

    def block_selected_device(self):
        """حجب الجهاز المحدد"""
        device = self.get_selected_device()
        if not device:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد جهاز من القائمة")
            return

        try:
            # تهيئة ARP Spoofer إذا لم يكن موجوداً
            if not self.arp_spoofer:
                self.arp_spoofer = ARPSpoofer(self.network_scanner.gateway_ip, self.network_scanner.network_interface)

            device_ip = device.get('ip')
            device_mac = device.get('mac')

            # حجب الجهاز
            success = self.arp_spoofer.block_device(device_ip, device_mac)

            if success:
                # حفظ الإعدادات في قاعدة البيانات
                self.db_manager.add_device(
                    mac_address=device_mac,
                    ip_address=device_ip,
                    device_name=device.get('hostname', ''),
                    action_type='block'
                )

                # تحديث الجدول
                self.refresh_devices()

                QMessageBox.information(self, "نجح", f"تم حجب الجهاز {device_ip} بنجاح")
                self.logger.log_device_blocked(device_ip, device_mac)
            else:
                QMessageBox.critical(self, "خطأ", f"فشل في حجب الجهاز {device_ip}")

        except Exception as e:
            self.logger.log_error(f"خطأ في حجب الجهاز: {e}", "errors")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حجب الجهاز:\n{e}")

    def unblock_selected_device(self):
        """إلغاء حجب الجهاز المحدد"""
        device = self.get_selected_device()
        if not device:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد جهاز من القائمة")
            return

        try:
            device_ip = device.get('ip')
            device_mac = device.get('mac')

            # إلغاء الحجب
            if self.arp_spoofer:
                success = self.arp_spoofer.unblock_device(device_ip)
            else:
                success = True

            # إزالة تحديد السرعة أيضاً
            self.bandwidth_controller.remove_device_limit(device_ip)

            if success:
                # تحديث الإعدادات في قاعدة البيانات
                self.db_manager.add_device(
                    mac_address=device_mac,
                    ip_address=device_ip,
                    device_name=device.get('hostname', ''),
                    action_type='allow'
                )

                # تحديث الجدول
                self.refresh_devices()

                QMessageBox.information(self, "نجح", f"تم إلغاء حجب الجهاز {device_ip} بنجاح")
                self.logger.log_device_unblocked(device_ip, device_mac)
            else:
                QMessageBox.critical(self, "خطأ", f"فشل في إلغاء حجب الجهاز {device_ip}")

        except Exception as e:
            self.logger.log_error(f"خطأ في إلغاء حجب الجهاز: {e}", "errors")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إلغاء حجب الجهاز:\n{e}")

    def limit_selected_device_speed(self):
        """تحديد سرعة الجهاز المحدد"""
        device = self.get_selected_device()
        if not device:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد جهاز من القائمة")
            return

        # فتح نافذة إعدادات الجهاز
        self.open_device_settings()

    def open_device_settings(self):
        """فتح نافذة إعدادات الجهاز المحدد"""
        device = self.get_selected_device()
        if not device:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد جهاز من القائمة")
            return

        try:
            # فتح نافذة الإعدادات
            dialog = DeviceSettingsDialog(device, self)

            # تحميل الإعدادات الحالية إذا كانت موجودة
            saved_settings = self.db_manager.get_device_settings(device.get('mac', ''))
            if saved_settings:
                dialog.hostname_edit.setText(saved_settings.get('device_name', ''))

                action_map = {'allow': 0, 'block': 1, 'limit': 2}
                dialog.action_combo.setCurrentIndex(action_map.get(saved_settings.get('action_type', 'allow'), 0))

                dialog.download_limit.setValue(saved_settings.get('download_limit', 0))
                dialog.upload_limit.setValue(saved_settings.get('upload_limit', 0))

            if dialog.exec_() == QDialog.Accepted:
                settings = dialog.get_settings()
                self.apply_device_settings_from_dialog(device, settings)

        except Exception as e:
            self.logger.log_error(f"خطأ في فتح إعدادات الجهاز: {e}", "errors")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح إعدادات الجهاز:\n{e}")

    def apply_device_settings_from_dialog(self, device: Dict, settings: Dict):
        """تطبيق الإعدادات من نافذة الحوار"""
        try:
            device_ip = device.get('ip')
            device_mac = device.get('mac')
            action = settings.get('action')

            # إزالة الإعدادات السابقة أولاً
            if self.arp_spoofer:
                self.arp_spoofer.unblock_device(device_ip)
            self.bandwidth_controller.remove_device_limit(device_ip)

            # تطبيق الإعدادات الجديدة
            if action == 'block':
                if not self.arp_spoofer:
                    self.arp_spoofer = ARPSpoofer(self.network_scanner.gateway_ip, self.network_scanner.network_interface)

                success = self.arp_spoofer.block_device(device_ip, device_mac)
                if success:
                    self.logger.log_device_blocked(device_ip, device_mac)

            elif action == 'limit':
                download_limit = settings.get('download_limit', 0)
                upload_limit = settings.get('upload_limit', 0)

                if download_limit > 0 or upload_limit > 0:
                    success = self.bandwidth_controller.limit_device_bandwidth(device_ip, download_limit, upload_limit)
                    if success:
                        self.logger.log_bandwidth_limited(device_ip, download_limit, upload_limit)

            # حفظ الإعدادات في قاعدة البيانات
            self.db_manager.add_device(
                mac_address=device_mac,
                ip_address=device_ip,
                device_name=settings.get('hostname', ''),
                action_type=action,
                download_limit=settings.get('download_limit', 0),
                upload_limit=settings.get('upload_limit', 0)
            )

            # تحديث الجدول
            self.refresh_devices()

            QMessageBox.information(self, "نجح", f"تم تطبيق الإعدادات على الجهاز {device_ip} بنجاح")
            self.logger.log_device_settings_saved(device_mac, settings)

        except Exception as e:
            self.logger.log_error(f"خطأ في تطبيق إعدادات الجهاز: {e}", "errors")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تطبيق الإعدادات:\n{e}")

    def block_all_devices(self):
        """حجب جميع الأجهزة"""
        if not self.current_devices:
            QMessageBox.warning(self, "تحذير", "لا توجد أجهزة للحجب")
            return

        reply = QMessageBox.question(
            self, "تأكيد",
            f"هل أنت متأكد من حجب جميع الأجهزة ({len(self.current_devices)} جهاز)؟\n"
            "هذا سيقطع الإنترنت عن جميع الأجهزة المتصلة.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if not self.arp_spoofer:
                    self.arp_spoofer = ARPSpoofer(self.network_scanner.gateway_ip, self.network_scanner.network_interface)

                blocked_count = 0
                for device in self.current_devices:
                    device_ip = device.get('ip')
                    device_mac = device.get('mac')

                    # تجاهل Gateway
                    if device_ip == self.network_scanner.gateway_ip:
                        continue

                    success = self.arp_spoofer.block_device(device_ip, device_mac)
                    if success:
                        blocked_count += 1

                        # حفظ في قاعدة البيانات
                        self.db_manager.add_device(
                            mac_address=device_mac,
                            ip_address=device_ip,
                            device_name=device.get('hostname', ''),
                            action_type='block'
                        )

                        self.logger.log_device_blocked(device_ip, device_mac)

                self.refresh_devices()
                QMessageBox.information(self, "نجح", f"تم حجب {blocked_count} جهاز بنجاح")
                self.logger.log_admin_action(f"حجب جميع الأجهزة - عدد الأجهزة المحجوبة: {blocked_count}")

            except Exception as e:
                self.logger.log_error(f"خطأ في حجب جميع الأجهزة: {e}", "errors")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حجب الأجهزة:\n{e}")

    def unblock_all_devices(self):
        """إلغاء حجب جميع الأجهزة"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل أنت متأكد من إلغاء حجب جميع الأجهزة؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # إلغاء جميع عمليات ARP spoofing
                if self.arp_spoofer:
                    self.arp_spoofer.unblock_all_devices()

                # إزالة جميع تحديدات السرعة
                self.bandwidth_controller.remove_all_limits()

                # تحديث قاعدة البيانات
                for device in self.current_devices:
                    device_mac = device.get('mac')
                    if device_mac:
                        self.db_manager.add_device(
                            mac_address=device_mac,
                            ip_address=device.get('ip', ''),
                            device_name=device.get('hostname', ''),
                            action_type='allow'
                        )

                self.refresh_devices()
                QMessageBox.information(self, "نجح", "تم إلغاء حجب جميع الأجهزة بنجاح")
                self.logger.log_admin_action("إلغاء حجب جميع الأجهزة")

            except Exception as e:
                self.logger.log_error(f"خطأ في إلغاء حجب جميع الأجهزة: {e}", "errors")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إلغاء حجب الأجهزة:\n{e}")

    def refresh_devices(self):
        """تحديث قائمة الأجهزة"""
        self.manual_scan()

    def load_saved_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            # تحميل إعدادات البرنامج
            auto_scan = self.db_manager.get_setting('auto_scan', 'true')
            self.auto_scan_check.setChecked(auto_scan.lower() == 'true')

            scan_interval = int(self.db_manager.get_setting('scan_interval', '30'))
            self.scan_interval.setValue(scan_interval)

            self.logger.log_info("تم تحميل الإعدادات المحفوظة", "database")

        except Exception as e:
            self.logger.log_error(f"خطأ في تحميل الإعدادات: {e}", "errors")

    def save_settings(self):
        """حفظ إعدادات البرنامج"""
        try:
            self.db_manager.save_setting('auto_scan', str(self.auto_scan_check.isChecked()).lower())
            self.db_manager.save_setting('scan_interval', str(self.scan_interval.value()))

            self.logger.log_info("تم حفظ إعدادات البرنامج", "database")

        except Exception as e:
            self.logger.log_error(f"خطأ في حفظ الإعدادات: {e}", "errors")

    def refresh_logs(self):
        """تحديث عرض السجلات"""
        try:
            logs = self.logger.get_recent_logs(100)

            # تنسيق السجلات للعرض
            log_text = ""
            for log in logs:
                timestamp = log['timestamp'][:19].replace('T', ' ')  # تنسيق التاريخ
                level_icon = {
                    'INFO': '📝',
                    'WARNING': '⚠️',
                    'ERROR': '❌',
                    'CRITICAL': '🚨',
                    'DEBUG': '🔍'
                }.get(log['level'], '📝')

                log_text += f"[{timestamp}] {level_icon} {log['level']} - {log['category']}: {log['message']}\n"

            self.logs_text.setPlainText(log_text)

            # التمرير إلى الأسفل لعرض أحدث السجلات
            scrollbar = self.logs_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

        except Exception as e:
            print(f"❌ خطأ في تحديث السجلات: {e}")

    def clear_logs(self):
        """مسح السجلات المعروضة"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل أنت متأكد من مسح جميع السجلات؟\n"
            "هذا سيمسح السجلات من الذاكرة فقط، وليس من الملفات.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.logger.recent_logs.clear()
            self.logs_text.clear()
            self.logger.log_info("تم مسح السجلات من الذاكرة", "database")

    def export_logs(self):
        """تصدير السجلات إلى ملف"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير السجلات",
                f"network_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "Text Files (*.txt);;All Files (*)"
            )

            if filename:
                success = self.logger.export_logs(filename)
                if success:
                    QMessageBox.information(self, "نجح", f"تم تصدير السجلات إلى:\n{filename}")
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في تصدير السجلات")

        except Exception as e:
            self.logger.log_error(f"خطأ في تصدير السجلات: {e}", "errors")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير السجلات:\n{e}")

    def closeEvent(self, event):
        """معالج إغلاق البرنامج"""
        try:
            # حفظ الإعدادات
            self.save_settings()

            # إيقاف جميع العمليات
            if self.scan_thread:
                self.scan_thread.stop()
                self.scan_thread.wait(3000)  # انتظار 3 ثوان

            if self.arp_spoofer:
                self.arp_spoofer.stop_all_spoofing()

            self.bandwidth_controller.stop_all_monitoring()

            # تسجيل إغلاق البرنامج
            self.logger.log_info("تم إغلاق البرنامج", "security")

            event.accept()

        except Exception as e:
            print(f"❌ خطأ أثناء إغلاق البرنامج: {e}")
            event.accept()

def check_admin_privileges():
    """فحص صلاحيات المدير"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """تشغيل البرنامج بصلاحيات المدير"""
    try:
        import ctypes
        import sys

        if not check_admin_privileges():
            # إعادة تشغيل البرنامج بصلاحيات المدير
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        return True
    except Exception as e:
        print(f"❌ خطأ في الحصول على صلاحيات المدير: {e}")
        return False

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""

    # فحص صلاحيات المدير
    if not check_admin_privileges():
        print("⚠️ البرنامج يحتاج إلى صلاحيات المدير للعمل بشكل كامل")
        print("🔄 محاولة إعادة التشغيل بصلاحيات المدير...")

        if not run_as_admin():
            print("❌ فشل في الحصول على صلاحيات المدير")
            input("اضغط Enter للمتابعة بدون صلاحيات المدير...")

    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setApplicationName("مدير الشبكة المتقدم")
    app.setApplicationVersion("1.0")

    # تعيين الخط العربي
    try:
        font = QFont("Segoe UI", 9)
        app.setFont(font)
    except:
        pass

    # إنشاء النافذة الرئيسية
    try:
        window = MainWindow()
        window.show()

        print("✅ تم تشغيل البرنامج بنجاح")
        print("🌐 مدير الشبكة المتقدم - Network Manager Pro")
        print("📝 جميع العمليات يتم تسجيلها في مجلد logs")

        # تشغيل التطبيق
        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في تشغيل البرنامج:\n{e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
