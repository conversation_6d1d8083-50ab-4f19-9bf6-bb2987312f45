"""
ملف الإعدادات والتكوين - إعدادات افتراضية للبرنامج
Configuration File - Default settings for the application
"""

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'db_path': 'network_manager.db',
    'backup_interval': 24,  # ساعات
    'max_logs': 10000,
    'auto_cleanup_days': 30
}

# إعدادات فحص الشبكة
NETWORK_SCAN_CONFIG = {
    'default_scan_interval': 30,  # ثانية
    'timeout': 2,  # ثانية
    'max_threads': 50,
    'ping_timeout': 1000,  # ميلي ثانية
    'arp_timeout': 2  # ثانية
}

# إعدادات ARP Spoofing
ARP_SPOOF_CONFIG = {
    'spoof_interval': 2,  # ثانية
    'restore_attempts': 3,
    'max_concurrent_targets': 50
}

# إعدادات التحكم في النطاق الترددي
BANDWIDTH_CONFIG = {
    'default_download_limit': 1000,  # KB/s
    'default_upload_limit': 500,    # KB/s
    'min_limit': 1,                 # KB/s
    'max_limit': 100000,            # KB/s
    'monitoring_interval': 5        # ثانية
}

# إعدادات السجلات
LOGGING_CONFIG = {
    'log_level': 'INFO',
    'max_log_size': 10,  # MB
    'backup_count': 5,
    'log_format': '%(asctime)s - %(levelname)s - %(message)s',
    'date_format': '%Y-%m-%d %H:%M:%S'
}

# إعدادات الواجهة الرسومية
GUI_CONFIG = {
    'window_width': 1200,
    'window_height': 800,
    'font_family': 'Segoe UI',
    'font_size': 9,
    'theme': 'default',
    'language': 'ar',
    'auto_refresh_interval': 5000,  # ميلي ثانية
    'table_row_height': 25
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'require_admin': True,
    'log_all_actions': True,
    'confirm_dangerous_actions': True,
    'auto_backup_settings': True,
    'encrypt_sensitive_data': False
}

# إعدادات الشبكة
NETWORK_CONFIG = {
    'default_gateway': '***********',
    'default_network_range': '***********/24',
    'excluded_ips': ['***********'],  # IPs لا يتم التحكم بها
    'max_devices': 254,
    'device_timeout': 300  # ثانية
}

# رسائل النظام
MESSAGES = {
    'ar': {
        'app_title': 'مدير الشبكة المتقدم',
        'scan_complete': 'تم اكتمال فحص الشبكة',
        'device_blocked': 'تم حجب الجهاز بنجاح',
        'device_unblocked': 'تم إلغاء حجب الجهاز بنجاح',
        'speed_limited': 'تم تحديد سرعة الجهاز بنجاح',
        'settings_saved': 'تم حفظ الإعدادات بنجاح',
        'error_occurred': 'حدث خطأ',
        'confirm_action': 'هل أنت متأكد؟',
        'admin_required': 'مطلوب صلاحيات المدير',
        'no_devices_found': 'لم يتم العثور على أجهزة',
        'select_device': 'يرجى تحديد جهاز'
    },
    'en': {
        'app_title': 'Advanced Network Manager',
        'scan_complete': 'Network scan completed',
        'device_blocked': 'Device blocked successfully',
        'device_unblocked': 'Device unblocked successfully',
        'speed_limited': 'Device speed limited successfully',
        'settings_saved': 'Settings saved successfully',
        'error_occurred': 'An error occurred',
        'confirm_action': 'Are you sure?',
        'admin_required': 'Administrator privileges required',
        'no_devices_found': 'No devices found',
        'select_device': 'Please select a device'
    }
}

# أيقونات الحالة
STATUS_ICONS = {
    'online': '🟢',
    'offline': '🔴',
    'blocked': '🚫',
    'limited': '🚦',
    'allowed': '✅',
    'unknown': '❓',
    'scanning': '🔍',
    'error': '❌',
    'warning': '⚠️',
    'info': 'ℹ️'
}

# ألوان الواجهة
COLORS = {
    'success': '#4CAF50',
    'error': '#f44336',
    'warning': '#ff9800',
    'info': '#2196F3',
    'primary': '#3f51b5',
    'secondary': '#9c27b0',
    'background': '#fafafa',
    'surface': '#ffffff',
    'text_primary': '#212121',
    'text_secondary': '#757575'
}

# إعدادات التصدير
EXPORT_CONFIG = {
    'default_format': 'txt',
    'supported_formats': ['txt', 'csv', 'json'],
    'include_timestamps': True,
    'include_categories': True,
    'max_export_records': 10000
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_interval': 24,  # ساعات
    'max_backups': 7,
    'backup_location': 'backups/',
    'compress_backups': True
}

def get_config(section: str, key: str = None, default=None):
    """
    الحصول على قيمة إعداد معين
    Get a specific configuration value
    
    Args:
        section: اسم القسم
        key: اسم المفتاح (اختياري)
        default: القيمة الافتراضية
    
    Returns:
        قيمة الإعداد أو القيمة الافتراضية
    """
    config_sections = {
        'database': DATABASE_CONFIG,
        'network_scan': NETWORK_SCAN_CONFIG,
        'arp_spoof': ARP_SPOOF_CONFIG,
        'bandwidth': BANDWIDTH_CONFIG,
        'logging': LOGGING_CONFIG,
        'gui': GUI_CONFIG,
        'security': SECURITY_CONFIG,
        'network': NETWORK_CONFIG,
        'messages': MESSAGES,
        'icons': STATUS_ICONS,
        'colors': COLORS,
        'export': EXPORT_CONFIG,
        'backup': BACKUP_CONFIG
    }
    
    if section not in config_sections:
        return default
    
    section_config = config_sections[section]
    
    if key is None:
        return section_config
    
    return section_config.get(key, default)

def get_message(key: str, language: str = 'ar') -> str:
    """
    الحصول على رسالة بلغة معينة
    Get a message in a specific language
    """
    messages = get_config('messages', language, {})
    return messages.get(key, key)

def get_icon(status: str) -> str:
    """
    الحصول على أيقونة حالة معينة
    Get an icon for a specific status
    """
    return get_config('icons', status, '❓')

def get_color(color_name: str) -> str:
    """
    الحصول على لون معين
    Get a specific color
    """
    return get_config('colors', color_name, '#000000')

# إعدادات افتراضية للتطبيق
DEFAULT_SETTINGS = {
    'auto_scan': True,
    'scan_interval': 30,
    'auto_apply_settings': True,
    'confirm_dangerous_actions': True,
    'log_level': 'INFO',
    'language': 'ar',
    'theme': 'default',
    'window_maximized': False,
    'show_tooltips': True,
    'auto_backup': True
}

def get_default_setting(key: str, default=None):
    """الحصول على إعداد افتراضي"""
    return DEFAULT_SETTINGS.get(key, default)

# معلومات البرنامج
APP_INFO = {
    'name': 'Network Manager Pro',
    'name_ar': 'مدير الشبكة المتقدم',
    'version': '1.0.0',
    'author': 'Python Developer',
    'description': 'Advanced Network Management Tool for Windows',
    'description_ar': 'أداة متقدمة لإدارة الشبكة لنظام ويندوز',
    'license': 'Educational Use',
    'website': 'https://github.com/network-manager-pro',
    'support_email': '<EMAIL>'
}

def get_app_info(key: str = None):
    """الحصول على معلومات البرنامج"""
    if key is None:
        return APP_INFO
    return APP_INFO.get(key, '')

# دالة للتحقق من صحة الإعدادات
def validate_config():
    """التحقق من صحة جميع الإعدادات"""
    errors = []
    
    # فحص إعدادات فحص الشبكة
    if NETWORK_SCAN_CONFIG['default_scan_interval'] < 5:
        errors.append("فترة الفحص يجب أن تكون 5 ثوان على الأقل")
    
    # فحص إعدادات النطاق الترددي
    if BANDWIDTH_CONFIG['min_limit'] >= BANDWIDTH_CONFIG['max_limit']:
        errors.append("الحد الأدنى للسرعة يجب أن يكون أقل من الحد الأقصى")
    
    # فحص إعدادات الواجهة
    if GUI_CONFIG['window_width'] < 800 or GUI_CONFIG['window_height'] < 600:
        errors.append("حجم النافذة صغير جداً")
    
    return errors

if __name__ == "__main__":
    # اختبار الإعدادات
    print("🧪 اختبار ملف الإعدادات...")
    
    errors = validate_config()
    if errors:
        print("❌ أخطاء في الإعدادات:")
        for error in errors:
            print(f"  • {error}")
    else:
        print("✅ جميع الإعدادات صحيحة")
    
    # عرض بعض الإعدادات
    print(f"\n📋 معلومات البرنامج:")
    print(f"  الاسم: {get_app_info('name_ar')}")
    print(f"  الإصدار: {get_app_info('version')}")
    
    print(f"\n⚙️ إعدادات افتراضية:")
    print(f"  فترة الفحص: {get_config('network_scan', 'default_scan_interval')} ثانية")
    print(f"  حد التحميل الافتراضي: {get_config('bandwidth', 'default_download_limit')} KB/s")
    print(f"  مستوى السجلات: {get_config('logging', 'log_level')}")
    
    print(f"\n🎨 الألوان:")
    print(f"  النجاح: {get_color('success')}")
    print(f"  الخطأ: {get_color('error')}")
    
    print(f"\n📝 الرسائل:")
    print(f"  عنوان التطبيق: {get_message('app_title')}")
    print(f"  تم حفظ الإعدادات: {get_message('settings_saved')}")
