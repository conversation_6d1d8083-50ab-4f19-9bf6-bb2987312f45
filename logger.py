"""
وحدة السجلات - تسجيل جميع العمليات والأحداث
Logger Module - Log all operations and events
"""

import logging
import os
from datetime import datetime
from typing import Optional, List, Dict
import json

class NetworkLogger:
    """فئة لتسجيل جميع أحداث وعمليات إدارة الشبكة"""
    
    def __init__(self, log_dir: str = "logs", log_level: int = logging.INFO):
        """
        تهيئة نظام السجلات
        
        Args:
            log_dir: مجلد حفظ ملفات السجلات
            log_level: مستوى التسجيل
        """
        self.log_dir = log_dir
        self.log_level = log_level
        
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # إعداد نظام التسجيل
        self._setup_logging()
        
        # قائمة السجلات في الذاكرة للعرض السريع
        self.recent_logs = []
        self.max_recent_logs = 1000
        
        print(f"📝 تم تهيئة نظام السجلات - مجلد: {log_dir}")
    
    def _setup_logging(self):
        """إعداد نظام التسجيل مع ملفات منفصلة لكل نوع"""
        
        # تنسيق الرسائل
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # إعداد logger رئيسي
        self.main_logger = logging.getLogger('NetworkManager')
        self.main_logger.setLevel(self.log_level)
        
        # ملف السجل الرئيسي
        main_handler = logging.FileHandler(
            os.path.join(self.log_dir, 'network_manager.log'),
            encoding='utf-8'
        )
        main_handler.setFormatter(formatter)
        self.main_logger.addHandler(main_handler)
        
        # إعداد loggers منفصلة لكل نوع من العمليات
        self.loggers = {}
        
        log_types = {
            'network_scan': 'فحص الشبكة',
            'arp_spoofing': 'ARP Spoofing',
            'bandwidth_control': 'التحكم في النطاق الترددي',
            'device_management': 'إدارة الأجهزة',
            'database': 'قاعدة البيانات',
            'errors': 'الأخطاء',
            'security': 'الأمان'
        }
        
        for log_type, description in log_types.items():
            logger = logging.getLogger(f'NetworkManager.{log_type}')
            logger.setLevel(self.log_level)
            
            handler = logging.FileHandler(
                os.path.join(self.log_dir, f'{log_type}.log'),
                encoding='utf-8'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
            self.loggers[log_type] = logger
        
        # إضافة console handler للعرض في الكونسول
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.WARNING)  # عرض التحذيرات والأخطاء فقط
        self.main_logger.addHandler(console_handler)
    
    def _add_to_recent_logs(self, level: str, message: str, category: str = "general"):
        """إضافة السجل إلى القائمة الحديثة في الذاكرة"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'message': message,
            'category': category
        }
        
        self.recent_logs.append(log_entry)
        
        # الحفاظ على حد أقصى من السجلات
        if len(self.recent_logs) > self.max_recent_logs:
            self.recent_logs.pop(0)
    
    def log_info(self, message: str, category: str = "general"):
        """تسجيل رسالة معلوماتية"""
        self.main_logger.info(message)
        if category in self.loggers:
            self.loggers[category].info(message)
        self._add_to_recent_logs("INFO", message, category)
    
    def log_warning(self, message: str, category: str = "general"):
        """تسجيل تحذير"""
        self.main_logger.warning(message)
        if category in self.loggers:
            self.loggers[category].warning(message)
        self._add_to_recent_logs("WARNING", message, category)
    
    def log_error(self, message: str, category: str = "errors", exception: Exception = None):
        """تسجيل خطأ"""
        if exception:
            message = f"{message} - Exception: {str(exception)}"
        
        self.main_logger.error(message)
        if category in self.loggers:
            self.loggers[category].error(message)
        self._add_to_recent_logs("ERROR", message, category)
    
    def log_critical(self, message: str, category: str = "errors"):
        """تسجيل خطأ حرج"""
        self.main_logger.critical(message)
        if category in self.loggers:
            self.loggers[category].critical(message)
        self._add_to_recent_logs("CRITICAL", message, category)
    
    def log_debug(self, message: str, category: str = "general"):
        """تسجيل رسالة تصحيح"""
        self.main_logger.debug(message)
        if category in self.loggers:
            self.loggers[category].debug(message)
        self._add_to_recent_logs("DEBUG", message, category)
    
    # دوال مخصصة لكل نوع من العمليات
    
    def log_network_scan(self, devices_found: int, scan_duration: float):
        """تسجيل عملية فحص الشبكة"""
        message = f"تم فحص الشبكة - عدد الأجهزة المكتشفة: {devices_found}, المدة: {scan_duration:.2f} ثانية"
        self.log_info(message, "network_scan")
    
    def log_device_discovered(self, ip: str, mac: str, hostname: str):
        """تسجيل اكتشاف جهاز جديد"""
        message = f"تم اكتشاف جهاز جديد - IP: {ip}, MAC: {mac}, الاسم: {hostname}"
        self.log_info(message, "network_scan")
    
    def log_device_blocked(self, ip: str, mac: str, method: str = "ARP Spoofing"):
        """تسجيل حجب جهاز"""
        message = f"تم حجب الجهاز {ip} ({mac}) باستخدام {method}"
        self.log_info(message, "arp_spoofing")
    
    def log_device_unblocked(self, ip: str, mac: str):
        """تسجيل إلغاء حجب جهاز"""
        message = f"تم إلغاء حجب الجهاز {ip} ({mac})"
        self.log_info(message, "arp_spoofing")
    
    def log_bandwidth_limited(self, ip: str, download_limit: int, upload_limit: int):
        """تسجيل تحديد سرعة جهاز"""
        message = f"تم تحديد سرعة الجهاز {ip} - تحميل: {download_limit} KB/s, رفع: {upload_limit} KB/s"
        self.log_info(message, "bandwidth_control")
    
    def log_bandwidth_limit_removed(self, ip: str):
        """تسجيل إزالة تحديد السرعة"""
        message = f"تم إزالة تحديد السرعة من الجهاز {ip}"
        self.log_info(message, "bandwidth_control")
    
    def log_device_settings_saved(self, mac: str, settings: Dict):
        """تسجيل حفظ إعدادات جهاز"""
        message = f"تم حفظ إعدادات الجهاز {mac} - الإعدادات: {json.dumps(settings, ensure_ascii=False)}"
        self.log_info(message, "device_management")
    
    def log_device_settings_loaded(self, mac: str, settings: Dict):
        """تسجيل تحميل إعدادات جهاز"""
        message = f"تم تحميل إعدادات الجهاز {mac} - الإعدادات: {json.dumps(settings, ensure_ascii=False)}"
        self.log_info(message, "device_management")
    
    def log_database_operation(self, operation: str, details: str = ""):
        """تسجيل عملية قاعدة بيانات"""
        message = f"عملية قاعدة البيانات: {operation}"
        if details:
            message += f" - التفاصيل: {details}"
        self.log_info(message, "database")
    
    def log_security_event(self, event: str, details: str = ""):
        """تسجيل حدث أمني"""
        message = f"حدث أمني: {event}"
        if details:
            message += f" - التفاصيل: {details}"
        self.log_warning(message, "security")
    
    def log_admin_action(self, action: str, user: str = "system"):
        """تسجيل إجراء إداري"""
        message = f"إجراء إداري بواسطة {user}: {action}"
        self.log_info(message, "security")
    
    def get_recent_logs(self, limit: int = 50, category: str = None) -> List[Dict]:
        """الحصول على السجلات الحديثة"""
        logs = self.recent_logs.copy()
        
        # تصفية حسب الفئة إذا تم تحديدها
        if category:
            logs = [log for log in logs if log['category'] == category]
        
        # إرجاع آخر عدد محدد من السجلات
        return logs[-limit:] if limit else logs
    
    def get_logs_by_date(self, date: str, category: str = None) -> List[str]:
        """الحصول على السجلات لتاريخ معين من الملفات"""
        try:
            log_file = os.path.join(self.log_dir, f'{category}.log' if category else 'network_manager.log')
            
            if not os.path.exists(log_file):
                return []
            
            logs = []
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if date in line:
                        logs.append(line.strip())
            
            return logs
            
        except Exception as e:
            self.log_error(f"خطأ في قراءة السجلات للتاريخ {date}", "errors", e)
            return []
    
    def clear_old_logs(self, days_to_keep: int = 30):
        """حذف السجلات القديمة"""
        try:
            cutoff_date = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
            
            for filename in os.listdir(self.log_dir):
                if filename.endswith('.log'):
                    file_path = os.path.join(self.log_dir, filename)
                    if os.path.getmtime(file_path) < cutoff_date:
                        os.remove(file_path)
                        print(f"🗑️ تم حذف ملف السجل القديم: {filename}")
            
            self.log_info(f"تم تنظيف السجلات القديمة - الاحتفاظ بآخر {days_to_keep} يوم", "database")
            
        except Exception as e:
            self.log_error("خطأ في تنظيف السجلات القديمة", "errors", e)
    
    def export_logs(self, output_file: str, category: str = None, date_range: tuple = None):
        """تصدير السجلات إلى ملف"""
        try:
            logs_to_export = self.get_recent_logs(category=category)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("# تقرير سجلات مدير الشبكة\n")
                f.write(f"# تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# الفئة: {category if category else 'جميع الفئات'}\n")
                f.write("=" * 80 + "\n\n")
                
                for log in logs_to_export:
                    f.write(f"[{log['timestamp']}] {log['level']} - {log['category']}: {log['message']}\n")
            
            self.log_info(f"تم تصدير السجلات إلى {output_file}", "database")
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في تصدير السجلات إلى {output_file}", "errors", e)
            return False

# إنشاء instance عام للاستخدام في جميع أنحاء البرنامج
network_logger = NetworkLogger()

# دالة مساعدة للاختبار
def test_logger():
    """اختبار نظام السجلات"""
    print("🧪 اختبار نظام السجلات...")
    
    logger = NetworkLogger("test_logs")
    
    # اختبار أنواع مختلفة من السجلات
    logger.log_info("بدء اختبار نظام السجلات")
    logger.log_network_scan(5, 2.5)
    logger.log_device_discovered("192.168.1.100", "AA:BB:CC:DD:EE:FF", "Test Device")
    logger.log_device_blocked("192.168.1.100", "AA:BB:CC:DD:EE:FF")
    logger.log_bandwidth_limited("192.168.1.100", 100, 50)
    logger.log_warning("هذا تحذير تجريبي")
    logger.log_error("هذا خطأ تجريبي")
    
    # عرض السجلات الحديثة
    recent_logs = logger.get_recent_logs(10)
    print(f"\n📋 السجلات الحديثة ({len(recent_logs)}):")
    for log in recent_logs:
        print(f"  [{log['timestamp']}] {log['level']} - {log['category']}: {log['message']}")

if __name__ == "__main__":
    test_logger()
