"""
وحدة اكتشاف الشبكة - فحص الأجهزة المتصلة بالشبكة
Network Scanner Module - Discover connected devices on the network
"""

import socket
import threading
import time
import subprocess
import re
import netifaces
from scapy.all import ARP, Ether, srp, get_if_addr, get_if_hwaddr
from typing import List, Dict, Optional
import ipaddress

class NetworkScanner:
    """فئة لاكتشاف الأجهزة المتصلة بالشبكة"""
    
    def __init__(self):
        """تهيئة ماسح الشبكة"""
        self.devices = []
        self.gateway_ip = None
        self.local_ip = None
        self.network_interface = None
        self.scanning = False
        
        # الحصول على معلومات الشبكة المحلية
        self._get_network_info()
    
    def _get_network_info(self):
        """الحصول على معلومات الشبكة المحلية (Gateway, Local IP, Interface)"""
        try:
            # الحصول على الواجهة الافتراضية
            gateways = netifaces.gateways()
            default_gateway = gateways['default'][netifaces.AF_INET]
            self.gateway_ip = default_gateway[0]
            self.network_interface = default_gateway[1]
            
            # الحصول على IP المحلي
            interface_info = netifaces.ifaddresses(self.network_interface)
            self.local_ip = interface_info[netifaces.AF_INET][0]['addr']
            
            print(f"🌐 معلومات الشبكة:")
            print(f"   Gateway: {self.gateway_ip}")
            print(f"   Local IP: {self.local_ip}")
            print(f"   Interface: {self.network_interface}")
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات الشبكة: {e}")
            # قيم افتراضية
            self.gateway_ip = "***********"
            self.local_ip = "***********00"
    
    def get_network_range(self) -> str:
        """الحصول على نطاق الشبكة المحلية"""
        try:
            if self.local_ip:
                # تحويل IP إلى network range
                network = ipaddress.IPv4Network(f"{self.local_ip}/24", strict=False)
                return str(network)
            return "***********/24"
        except:
            return "***********/24"
    
    def scan_network_arp(self) -> List[Dict]:
        """
        فحص الشبكة باستخدام ARP requests
        Scan network using ARP requests
        """
        devices = []
        network_range = self.get_network_range()
        
        try:
            print(f"🔍 بدء فحص الشبكة: {network_range}")
            
            # إنشاء ARP request
            arp_request = ARP(pdst=network_range)
            broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
            arp_request_broadcast = broadcast / arp_request
            
            # إرسال الطلبات والحصول على الردود
            answered_list = srp(arp_request_broadcast, timeout=2, verbose=False)[0]
            
            for element in answered_list:
                device_info = {
                    'ip': element[1].psrc,
                    'mac': element[1].hwsrc.upper(),
                    'hostname': self._get_hostname(element[1].psrc),
                    'vendor': self._get_vendor_from_mac(element[1].hwsrc),
                    'status': 'online',
                    'last_seen': time.time()
                }
                devices.append(device_info)
            
            # إضافة معلومات Gateway
            if self.gateway_ip:
                gateway_mac = self._get_mac_address(self.gateway_ip)
                if gateway_mac:
                    gateway_device = {
                        'ip': self.gateway_ip,
                        'mac': gateway_mac.upper(),
                        'hostname': 'Gateway/Router',
                        'vendor': self._get_vendor_from_mac(gateway_mac),
                        'status': 'online',
                        'last_seen': time.time()
                    }
                    # التأكد من عدم تكرار Gateway
                    if not any(d['ip'] == self.gateway_ip for d in devices):
                        devices.append(gateway_device)
            
            print(f"✅ تم العثور على {len(devices)} جهاز")
            return devices
            
        except Exception as e:
            print(f"❌ خطأ في فحص الشبكة: {e}")
            return []
    
    def _get_hostname(self, ip: str) -> str:
        """الحصول على اسم الجهاز من IP"""
        try:
            hostname = socket.gethostbyaddr(ip)[0]
            return hostname
        except:
            return f"Device-{ip.split('.')[-1]}"
    
    def _get_mac_address(self, ip: str) -> Optional[str]:
        """الحصول على MAC address لجهاز معين"""
        try:
            # استخدام ARP table في Windows
            result = subprocess.run(['arp', '-a', ip], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                # البحث عن MAC address في النتيجة
                mac_pattern = r'([0-9a-fA-F]{2}[:-]){5}([0-9a-fA-F]{2})'
                match = re.search(mac_pattern, result.stdout)
                if match:
                    return match.group(0).replace('-', ':')
            
            # طريقة بديلة باستخدام scapy
            arp_request = ARP(pdst=ip)
            broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
            arp_request_broadcast = broadcast / arp_request
            answered_list = srp(arp_request_broadcast, timeout=1, verbose=False)[0]
            
            if answered_list:
                return answered_list[0][1].hwsrc.upper()
                
        except Exception as e:
            print(f"❌ خطأ في الحصول على MAC address لـ {ip}: {e}")
        
        return None
    
    def _get_vendor_from_mac(self, mac: str) -> str:
        """الحصول على اسم الشركة المصنعة من MAC address"""
        # قاموس مبسط لبعض الشركات المشهورة
        vendor_prefixes = {
            '00:50:56': 'VMware',
            '08:00:27': 'VirtualBox',
            '00:0C:29': 'VMware',
            '00:1B:21': 'Intel',
            '00:23:24': 'Apple',
            '28:CF:E9': 'Apple',
            '3C:07:54': 'Apple',
            '00:26:BB': 'Apple',
            'DC:A6:32': 'Raspberry Pi',
            'B8:27:EB': 'Raspberry Pi',
            '00:15:5D': 'Microsoft',
            '00:03:FF': 'Microsoft',
            '52:54:00': 'QEMU/KVM',
        }
        
        try:
            mac_prefix = mac[:8].upper()
            return vendor_prefixes.get(mac_prefix, 'Unknown')
        except:
            return 'Unknown'
    
    def ping_device(self, ip: str) -> bool:
        """فحص إذا كان الجهاز متصل (ping test)"""
        try:
            # استخدام ping command في Windows
            result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                                  capture_output=True, text=True, timeout=3)
            return result.returncode == 0
        except:
            return False
    
    def continuous_scan(self, callback=None, interval: int = 30):
        """فحص مستمر للشبكة مع callback للتحديثات"""
        self.scanning = True
        
        def scan_loop():
            while self.scanning:
                try:
                    devices = self.scan_network_arp()
                    self.devices = devices
                    
                    if callback:
                        callback(devices)
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    print(f"❌ خطأ في الفحص المستمر: {e}")
                    time.sleep(5)
        
        scan_thread = threading.Thread(target=scan_loop, daemon=True)
        scan_thread.start()
        return scan_thread
    
    def stop_scanning(self):
        """إيقاف الفحص المستمر"""
        self.scanning = False
    
    def get_current_devices(self) -> List[Dict]:
        """الحصول على قائمة الأجهزة الحالية"""
        return self.devices.copy()
    
    def is_device_online(self, ip: str) -> bool:
        """فحص إذا كان الجهاز متصل حالياً"""
        return self.ping_device(ip)

# دالة مساعدة للاختبار
def test_scanner():
    """اختبار وحدة فحص الشبكة"""
    scanner = NetworkScanner()
    
    print("🧪 اختبار فحص الشبكة...")
    devices = scanner.scan_network_arp()
    
    print(f"\n📱 الأجهزة المكتشفة ({len(devices)}):")
    for device in devices:
        print(f"  IP: {device['ip']:<15} MAC: {device['mac']:<17} Name: {device['hostname']}")

if __name__ == "__main__":
    test_scanner()
