# مدير الشبكة المتقدم - Network Manager Pro

برنامج شامل لإدارة الشبكة المحلية مع إمكانيات متقدمة للتحكم في الأجهزة المتصلة، مطور خصيصاً لنظام Windows 11.

## 🌟 الميزات الرئيسية

### 🔍 اكتشاف الشبكة
- فحص تلقائي ويدوي للأجهزة المتصلة بالشبكة
- عرض معلومات مفصلة لكل جهاز (IP, MAC, اسم الجهاز)
- مراقبة مستمرة للأجهزة الجديدة والمنقطعة

### 🚫 حجب الأجهزة (ARP Spoofing)
- حجب أي جهاز من الوصول للإنترنت
- تقنية ARP Spoofing المتقدمة
- إمكانية حجب جميع الأجهزة دفعة واحدة
- استعادة الاتصال الطبيعي بسهولة

### 🚦 التحكم في سرعة الإنترنت
- تحديد سرعة التحميل والرفع لكل جهاز
- استخدام تقنيات QoS المتقدمة
- مراقبة استهلاك البيانات

### 💾 حفظ الإعدادات التلقائي
- حفظ إعدادات كل جهاز بناءً على MAC Address
- تطبيق الإعدادات تلقائياً عند إعادة الاتصال
- قاعدة بيانات SQLite محلية

### 📊 نظام السجلات المتقدم
- تسجيل جميع العمليات والأحداث
- سجلات منفصلة لكل نوع من العمليات
- إمكانية تصدير السجلات
- عرض السجلات في الوقت الفعلي

### 🎨 واجهة رسومية متقدمة
- واجهة PyQt5 عربية سهلة الاستخدام
- جداول تفاعلية لعرض الأجهزة
- نوافذ إعدادات مفصلة لكل جهاز
- شريط حالة يعرض معلومات الشبكة

## 📋 المتطلبات

### متطلبات النظام
- Windows 11 (أو Windows 10)
- Python 3.7 أو أحدث
- صلاحيات المدير (Administrator)
- Npcap أو WinPcap

### المكتبات المطلوبة
```
PyQt5==5.15.9
scapy==2.5.0
psutil==5.9.5
netifaces==0.11.0
```

## 🚀 التثبيت والتشغيل

### الطريقة الأولى: التثبيت التلقائي
1. تحميل جميع الملفات
2. تشغيل سكريبت التثبيت:
```bash
python install_requirements.py
```
3. اتباع التعليمات لتثبيت Npcap
4. تشغيل البرنامج:
```bash
python gui_main.py
```

### الطريقة الثانية: التثبيت اليدوي
1. تثبيت Python 3.7+ من [python.org](https://python.org)
2. تثبيت المكتبات المطلوبة:
```bash
pip install PyQt5==5.15.9 scapy==2.5.0 psutil==5.9.5 netifaces==0.11.0
```
3. تحميل وتثبيت Npcap من [nmap.org/npcap](https://nmap.org/npcap/)
   - ⚠️ تأكد من تحديد خيار "Install Npcap in WinPcap API-compatible Mode"
4. تشغيل البرنامج بصلاحيات المدير:
```bash
python gui_main.py
```

## 📖 دليل الاستخدام

### البدء السريع
1. تشغيل البرنامج بصلاحيات المدير
2. انتظار اكتمال فحص الشبكة التلقائي
3. اختيار جهاز من القائمة
4. استخدام الأزرار للتحكم في الجهاز

### إعدادات الجهاز
1. تحديد جهاز من الجدول
2. النقر على "⚙️ إعدادات الجهاز"
3. اختيار الإجراء المطلوب:
   - **السماح**: السماح بالوصول الطبيعي للإنترنت
   - **حجب**: منع الوصول للإنترنت نهائياً
   - **تحديد السرعة**: تحديد سرعة التحميل والرفع
4. حفظ الإعدادات

### مراقبة السجلات
- الانتقال إلى تبويب "📋 السجلات"
- مراقبة جميع العمليات في الوقت الفعلي
- تصدير السجلات عند الحاجة

## 🏗️ هيكل المشروع

```
Network-Manager-Pro/
├── gui_main.py              # الواجهة الرسومية الرئيسية
├── database.py              # إدارة قاعدة البيانات
├── network_scanner.py       # فحص واكتشاف الشبكة
├── arp_spoofer.py          # وحدة ARP Spoofing
├── bandwidth_controller.py  # التحكم في النطاق الترددي
├── logger.py               # نظام السجلات
├── install_requirements.py # سكريبت التثبيت
├── requirements.txt        # قائمة المتطلبات
├── README.md              # هذا الملف
└── logs/                  # مجلد السجلات (يتم إنشاؤه تلقائياً)
```

## ⚙️ الوحدات والمكونات

### 🗄️ قاعدة البيانات (database.py)
- إدارة بيانات الأجهزة والإعدادات
- حفظ السجلات والإحصائيات
- نظام backup تلقائي

### 🔍 فحص الشبكة (network_scanner.py)
- اكتشاف الأجهزة باستخدام ARP scanning
- تحديد معلومات الشبكة تلقائياً
- مراقبة مستمرة للتغييرات

### 🎯 ARP Spoofing (arp_spoofer.py)
- تنفيذ هجمات ARP spoofing آمنة
- حجب الأجهزة من الإنترنت
- استعادة الاتصال الطبيعي

### 🚦 التحكم في السرعة (bandwidth_controller.py)
- استخدام QoS policies في Windows
- تحديد سرعة التحميل والرفع
- مراقبة استهلاك البيانات

### 📝 نظام السجلات (logger.py)
- تسجيل جميع العمليات
- سجلات منفصلة لكل فئة
- تصدير وأرشفة السجلات

## 🔒 الأمان والخصوصية

- جميع العمليات تتم محلياً على الشبكة المحلية فقط
- لا يتم إرسال أي بيانات خارج الشبكة المحلية
- السجلات محفوظة محلياً فقط
- إمكانية إيقاف جميع العمليات فوراً

## ⚠️ تحذيرات مهمة

1. **استخدم البرنامج بمسؤولية**: هذا البرنامج مخصص لإدارة شبكتك المحلية فقط
2. **صلاحيات المدير مطلوبة**: للوصول إلى واجهات الشبكة وتعديل إعدادات النظام
3. **Npcap ضروري**: scapy يحتاج إلى Npcap للعمل على Windows
4. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية من قاعدة البيانات

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ: "No module named 'scapy'"**
```bash
pip install scapy==2.5.0
```

**خطأ: "WinPcap is not installed"**
- تحميل وتثبيت Npcap من [nmap.org/npcap](https://nmap.org/npcap/)

**خطأ: "Access Denied"**
- تشغيل البرنامج بصلاحيات المدير

**لا يظهر أي أجهزة**
- فحص اتصال الشبكة
- التأكد من أن الجهاز متصل بنفس الشبكة

## 📞 الدعم والمساعدة

- جميع السجلات محفوظة في مجلد `logs/`
- فحص ملف `network_manager.log` للأخطاء العامة
- فحص ملفات السجلات المتخصصة لمشاكل محددة

## 📄 الترخيص

هذا المشروع مطور لأغراض تعليمية وإدارة الشبكات المحلية. يرجى استخدامه بمسؤولية وفقط على الشبكات التي تملك صلاحية إدارتها.

## 🔄 التحديثات المستقبلية

- [ ] دعم IPv6
- [ ] واجهة ويب اختيارية
- [ ] تقارير مفصلة
- [ ] دعم شبكات متعددة
- [ ] تشفير قاعدة البيانات

---

**تم التطوير بواسطة**: مطور Python متخصص  
**الإصدار**: 1.0  
**تاريخ الإصدار**: 2024  
**التوافق**: Windows 11/10, Python 3.7+
