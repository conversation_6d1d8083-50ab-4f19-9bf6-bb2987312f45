# فهرس الملفات - Files Index

## 📁 مدير الشبكة المتقدم - Network Manager Pro

### 🏗️ الملفات الأساسية (Core Files)

| الملف | الوصف | الاستخدام |
|-------|--------|-----------|
| `gui_main.py` | الواجهة الرسومية الرئيسية | الملف الرئيسي للبرنامج |
| `database.py` | إدارة قاعدة البيانات | حفظ الإعدادات والسجلات |
| `network_scanner.py` | فحص واكتشاف الشبكة | اكتشاف الأجهزة المتصلة |
| `arp_spoofer.py` | وحدة ARP Spoofing | حجب الأجهزة من الإنترنت |
| `bandwidth_controller.py` | التحكم في النطاق الترددي | تحديد سرعة الأجهزة |
| `logger.py` | نظام السجلات | تسجيل جميع العمليات |
| `config.py` | إعدادات وتكوين البرنامج | الإعدادات الافتراضية |

### 🛠️ ملفات التثبيت والإعداد (Installation & Setup)

| الملف | الوصف | الاستخدام |
|-------|--------|-----------|
| `install_requirements.py` | تثبيت المتطلبات | تثبيت تلقائي للمكتبات |
| `setup.bat` | إعداد كامل للبرنامج | إعداد شامل بنقرة واحدة |
| `requirements.txt` | قائمة المتطلبات | المكتبات المطلوبة |

### 🚀 ملفات التشغيل (Launch Files)

| الملف | الوصف | الاستخدام |
|-------|--------|-----------|
| `start.py` | نقطة الدخول الذكية | تشغيل مع فحص المتطلبات |
| `run_network_manager.bat` | تشغيل البرنامج | تشغيل مع فحص الأخطاء |
| `quick_start.bat` | تشغيل سريع | تشغيل مباشر |

### 🧪 ملفات الاختبار والعرض (Testing & Demo)

| الملف | الوصف | الاستخدام |
|-------|--------|-----------|
| `test_all_modules.py` | اختبار شامل للمكونات | فحص عمل جميع الوحدات |
| `demo.py` | عرض توضيحي آمن | عرض الميزات بدون تأثير حقيقي |
| `run_demo.bat` | تشغيل العرض التوضيحي | عرض آمن للميزات |

### 📚 ملفات التوثيق (Documentation)

| الملف | الوصف | المحتوى |
|-------|--------|---------|
| `README.md` | دليل المستخدم الشامل | تعليمات التثبيت والاستخدام |
| `CHANGELOG.md` | سجل التغييرات | تاريخ الإصدارات والتحديثات |
| `FILES_INDEX.md` | فهرس الملفات | هذا الملف - دليل جميع الملفات |

---

## 📂 هيكل المجلدات (Directory Structure)

```
Network-Manager-Pro/
├── 📄 الملفات الأساسية
│   ├── gui_main.py
│   ├── database.py
│   ├── network_scanner.py
│   ├── arp_spoofer.py
│   ├── bandwidth_controller.py
│   ├── logger.py
│   └── config.py
│
├── 🛠️ ملفات التثبيت
│   ├── install_requirements.py
│   ├── setup.bat
│   └── requirements.txt
│
├── 🚀 ملفات التشغيل
│   ├── start.py
│   ├── run_network_manager.bat
│   └── quick_start.bat
│
├── 🧪 ملفات الاختبار
│   ├── test_all_modules.py
│   ├── demo.py
│   └── run_demo.bat
│
├── 📚 التوثيق
│   ├── README.md
│   ├── CHANGELOG.md
│   └── FILES_INDEX.md
│
└── 📁 مجلدات يتم إنشاؤها تلقائياً
    ├── logs/           # ملفات السجلات
    ├── backups/        # النسخ الاحتياطية
    └── *.db           # ملفات قاعدة البيانات
```

---

## 🎯 دليل الاستخدام السريع

### للمبتدئين
1. **الإعداد الكامل**: تشغيل `setup.bat`
2. **التشغيل**: تشغيل `quick_start.bat`
3. **العرض التوضيحي**: تشغيل `run_demo.bat`

### للمطورين
1. **اختبار المكونات**: `python test_all_modules.py`
2. **تشغيل متقدم**: `python start.py`
3. **تشغيل مباشر**: `python gui_main.py`

### لاستكشاف الأخطاء
1. **فحص المتطلبات**: `python install_requirements.py`
2. **اختبار شامل**: `python test_all_modules.py`
3. **مراجعة السجلات**: فحص مجلد `logs/`

---

## 📋 تفاصيل الملفات

### 🖥️ gui_main.py
- **الحجم**: ~950 سطر
- **الوظيفة**: الواجهة الرسومية الرئيسية
- **المكونات**: 
  - جدول الأجهزة التفاعلي
  - نوافذ إعدادات الأجهزة
  - تبويبات السجلات والإعدادات
  - معالجة الأحداث والإشارات

### 🗄️ database.py
- **الحجم**: ~300 سطر
- **الوظيفة**: إدارة قاعدة البيانات SQLite
- **الجداول**:
  - `devices`: إعدادات الأجهزة
  - `logs`: سجل العمليات
  - `settings`: إعدادات البرنامج

### 🔍 network_scanner.py
- **الحجم**: ~300 سطر
- **الوظيفة**: فحص واكتشاف الشبكة
- **الميزات**:
  - فحص ARP للشبكة المحلية
  - اكتشاف معلومات الأجهزة
  - مراقبة مستمرة

### 🎯 arp_spoofer.py
- **الحجم**: ~300 سطر
- **الوظيفة**: تنفيذ ARP Spoofing
- **الميزات**:
  - حجب الأجهزة من الإنترنت
  - استعادة الاتصال الطبيعي
  - إدارة عدة أجهزة

### 🚦 bandwidth_controller.py
- **الحجم**: ~300 سطر
- **الوظيفة**: التحكم في سرعة الإنترنت
- **الميزات**:
  - تحديد سرعة التحميل والرفع
  - استخدام QoS policies
  - مراقبة استهلاك البيانات

### 📝 logger.py
- **الحجم**: ~300 سطر
- **الوظيفة**: نظام السجلات المتقدم
- **الميزات**:
  - سجلات منفصلة لكل فئة
  - تصدير وأرشفة السجلات
  - عرض في الوقت الفعلي

---

## 🔧 متطلبات التشغيل

### البرمجيات المطلوبة
- **Python 3.7+**: اللغة الأساسية
- **PyQt5**: الواجهة الرسومية
- **Scapy**: التعامل مع حزم الشبكة
- **Npcap**: مطلوب لـ Scapy على Windows

### صلاحيات النظام
- **صلاحيات المدير**: مطلوبة لبعض الميزات
- **الوصول للشبكة**: للتعامل مع واجهات الشبكة
- **كتابة الملفات**: لحفظ السجلات والإعدادات

---

## 🛡️ الأمان والخصوصية

### البيانات المحلية
- جميع البيانات محفوظة محلياً
- لا يتم إرسال أي معلومات خارج الشبكة المحلية
- تشفير اختياري للبيانات الحساسة

### السجلات الأمنية
- تسجيل جميع العمليات الحساسة
- مراقبة محاولات الوصول غير المصرح
- نسخ احتياطية دورية للإعدادات

---

## 📞 الدعم والمساعدة

### الموارد المتاحة
- **التوثيق الشامل**: README.md
- **سجل التغييرات**: CHANGELOG.md
- **اختبارات شاملة**: test_all_modules.py
- **عرض توضيحي**: demo.py

### استكشاف الأخطاء
1. مراجعة ملفات السجلات في `logs/`
2. تشغيل الاختبارات الشاملة
3. فحص متطلبات النظام
4. التأكد من صلاحيات المدير

---

*آخر تحديث: 2024*
