#!/usr/bin/env python3
"""
نقطة الدخول الرئيسية للبرنامج - تشغيل سريع مع فحص المتطلبات
Main entry point - Quick start with requirements check
"""

import sys
import os
import subprocess
import ctypes
from pathlib import Path

def check_python_version():
    """فحص إصدار Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print(f"🐍 الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
        print("📥 يرجى تحديث Python من: https://python.org")
        return False
    return True

def check_admin_privileges():
    """فحص صلاحيات المدير"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """تشغيل البرنامج بصلاحيات المدير"""
    try:
        if not check_admin_privileges():
            print("🔄 إعادة تشغيل البرنامج بصلاحيات المدير...")
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            sys.exit(0)
    except Exception as e:
        print(f"❌ خطأ في الحصول على صلاحيات المدير: {e}")
        return False
    return True

def check_required_files():
    """فحص وجود الملفات المطلوبة"""
    required_files = [
        'gui_main.py',
        'database.py', 
        'network_scanner.py',
        'arp_spoofer.py',
        'bandwidth_controller.py',
        'logger.py',
        'config.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   • {file}")
        return False
    
    return True

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    required_packages = [
        'PyQt5',
        'scapy', 
        'psutil',
        'netifaces'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ مكتبات مفقودة:")
        for package in missing_packages:
            print(f"   • {package}")
        
        print("\n💡 لتثبيت المكتبات المفقودة:")
        print("   python install_requirements.py")
        print("   أو")
        print("   pip install " + " ".join(missing_packages))
        return False
    
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['logs', 'backups']
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
        except Exception as e:
            print(f"⚠️ تحذير: فشل في إنشاء مجلد {directory}: {e}")

def show_startup_info():
    """عرض معلومات البدء"""
    print("=" * 60)
    print("🚀 مدير الشبكة المتقدم - Network Manager Pro")
    print("📡 Advanced Network Management Tool")
    print("=" * 60)
    print(f"🐍 Python: {sys.version.split()[0]}")
    print(f"💻 النظام: {os.name}")
    print(f"👤 صلاحيات المدير: {'نعم' if check_admin_privileges() else 'لا'}")
    print("=" * 60)

def main():
    """الدالة الرئيسية"""
    show_startup_info()
    
    # فحص إصدار Python
    print("🔍 فحص إصدار Python...")
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return 1
    print("✅ إصدار Python مناسب")
    
    # فحص الملفات المطلوبة
    print("\n📁 فحص الملفات المطلوبة...")
    if not check_required_files():
        print("❌ بعض الملفات مفقودة")
        input("اضغط Enter للخروج...")
        return 1
    print("✅ جميع الملفات موجودة")
    
    # فحص المكتبات
    print("\n📦 فحص المكتبات المطلوبة...")
    if not check_dependencies():
        print("\n❌ بعض المكتبات مفقودة")
        
        choice = input("\nهل تريد تشغيل مثبت المتطلبات؟ (y/n): ").lower()
        if choice == 'y':
            try:
                subprocess.run([sys.executable, 'install_requirements.py'], check=True)
                print("\n🔄 إعادة فحص المكتبات...")
                if not check_dependencies():
                    print("❌ لا تزال بعض المكتبات مفقودة")
                    input("اضغط Enter للخروج...")
                    return 1
            except subprocess.CalledProcessError:
                print("❌ فشل في تثبيت المتطلبات")
                input("اضغط Enter للخروج...")
                return 1
            except FileNotFoundError:
                print("❌ ملف install_requirements.py غير موجود")
                input("اضغط Enter للخروج...")
                return 1
        else:
            input("اضغط Enter للخروج...")
            return 1
    
    print("✅ جميع المكتبات متوفرة")
    
    # فحص صلاحيات المدير
    print("\n🔐 فحص صلاحيات المدير...")
    if not check_admin_privileges():
        print("⚠️ البرنامج لا يعمل بصلاحيات المدير")
        print("🔧 بعض الميزات قد لا تعمل بشكل كامل")
        
        choice = input("\nهل تريد إعادة التشغيل بصلاحيات المدير؟ (y/n): ").lower()
        if choice == 'y':
            run_as_admin()
            return 0
        else:
            print("⚠️ المتابعة بدون صلاحيات المدير...")
    else:
        print("✅ البرنامج يعمل بصلاحيات المدير")
    
    # إنشاء المجلدات المطلوبة
    print("\n📁 إنشاء المجلدات المطلوبة...")
    create_directories()
    print("✅ تم إنشاء المجلدات")
    
    # تشغيل البرنامج الرئيسي
    print("\n🚀 بدء تشغيل البرنامج...")
    print("=" * 60)
    
    try:
        # استيراد وتشغيل الواجهة الرئيسية
        from gui_main import main as gui_main
        return gui_main()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الواجهة الرئيسية: {e}")
        print("🔧 تأكد من وجود ملف gui_main.py")
        input("اضغط Enter للخروج...")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        print("📝 تحقق من ملفات السجلات في مجلد logs/")
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ حرج: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
