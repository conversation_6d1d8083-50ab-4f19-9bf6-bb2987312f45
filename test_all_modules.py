"""
اختبار شامل لجميع وحدات البرنامج
Comprehensive test for all program modules
"""

import sys
import os
import time
import traceback
from datetime import datetime

def test_imports():
    """اختبار استيراد جميع المكتبات المطلوبة"""
    print("🧪 اختبار استيراد المكتبات...")
    
    required_modules = [
        ('PyQt5.QtWidgets', 'PyQt5'),
        ('PyQt5.QtCore', 'PyQt5'),
        ('PyQt5.QtGui', 'PyQt5'),
        ('scapy.all', 'scapy'),
        ('psutil', 'psutil'),
        ('netifaces', 'netifaces'),
        ('sqlite3', 'sqlite3 (built-in)'),
        ('threading', 'threading (built-in)'),
        ('subprocess', 'subprocess (built-in)'),
        ('socket', 'socket (built-in)'),
        ('ctypes', 'ctypes (built-in)')
    ]
    
    success_count = 0
    failed_modules = []
    
    for module_name, display_name in required_modules:
        try:
            __import__(module_name)
            print(f"  ✅ {display_name}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {display_name} - خطأ: {e}")
            failed_modules.append(display_name)
        except Exception as e:
            print(f"  ⚠️ {display_name} - تحذير: {e}")
    
    print(f"\n📊 نتائج الاستيراد: {success_count}/{len(required_modules)} نجح")
    
    if failed_modules:
        print("❌ المكتبات الفاشلة:")
        for module in failed_modules:
            print(f"   • {module}")
        return False
    
    return True

def test_database():
    """اختبار وحدة قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database import DatabaseManager
        
        # إنشاء قاعدة بيانات اختبار
        db = DatabaseManager("test_network.db")
        
        # اختبار إضافة جهاز
        success = db.add_device(
            mac_address="AA:BB:CC:DD:EE:FF",
            ip_address="***********00",
            device_name="Test Device",
            action_type="block",
            download_limit=100,
            upload_limit=50
        )
        
        if success:
            print("  ✅ إضافة جهاز")
        else:
            print("  ❌ إضافة جهاز")
            return False
        
        # اختبار استرجاع إعدادات الجهاز
        settings = db.get_device_settings("AA:BB:CC:DD:EE:FF")
        if settings and settings['action_type'] == 'block':
            print("  ✅ استرجاع إعدادات الجهاز")
        else:
            print("  ❌ استرجاع إعدادات الجهاز")
            return False
        
        # اختبار إضافة سجل
        db.add_log("AA:BB:CC:DD:EE:FF", "اختبار السجل", "تفاصيل الاختبار")
        logs = db.get_logs(10)
        if logs and len(logs) > 0:
            print("  ✅ نظام السجلات")
        else:
            print("  ❌ نظام السجلات")
            return False
        
        # تنظيف ملف الاختبار
        try:
            os.remove("test_network.db")
        except:
            pass
        
        print("  ✅ قاعدة البيانات تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في قاعدة البيانات: {e}")
        traceback.print_exc()
        return False

def test_network_scanner():
    """اختبار وحدة فحص الشبكة"""
    print("\n🔍 اختبار فحص الشبكة...")
    
    try:
        from network_scanner import NetworkScanner
        
        scanner = NetworkScanner()
        
        # اختبار الحصول على معلومات الشبكة
        if scanner.gateway_ip and scanner.local_ip:
            print(f"  ✅ معلومات الشبكة - Gateway: {scanner.gateway_ip}, Local: {scanner.local_ip}")
        else:
            print("  ⚠️ لم يتم العثور على معلومات الشبكة")
        
        # اختبار الحصول على نطاق الشبكة
        network_range = scanner.get_network_range()
        if network_range:
            print(f"  ✅ نطاق الشبكة: {network_range}")
        else:
            print("  ❌ فشل في الحصول على نطاق الشبكة")
            return False
        
        # اختبار ping للـ gateway
        if scanner.gateway_ip:
            ping_result = scanner.ping_device(scanner.gateway_ip)
            if ping_result:
                print("  ✅ ping للـ gateway")
            else:
                print("  ⚠️ فشل ping للـ gateway")
        
        print("  ✅ فحص الشبكة يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في فحص الشبكة: {e}")
        traceback.print_exc()
        return False

def test_arp_spoofer():
    """اختبار وحدة ARP Spoofing"""
    print("\n🎯 اختبار ARP Spoofer...")
    
    try:
        from arp_spoofer import ARPSpoofer
        from network_scanner import NetworkScanner
        
        scanner = NetworkScanner()
        spoofer = ARPSpoofer(scanner.gateway_ip or "***********")
        
        # اختبار الحصول على MAC address للـ gateway
        if scanner.gateway_ip:
            gateway_mac = spoofer.get_mac_address(scanner.gateway_ip)
            if gateway_mac:
                print(f"  ✅ MAC address للـ gateway: {gateway_mac}")
            else:
                print("  ⚠️ لم يتم العثور على MAC address للـ gateway")
        
        # اختبار حالة ARP Spoofer
        status = spoofer.get_status()
        if isinstance(status, dict):
            print("  ✅ حالة ARP Spoofer")
        else:
            print("  ❌ خطأ في حالة ARP Spoofer")
            return False
        
        print("  ✅ ARP Spoofer يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في ARP Spoofer: {e}")
        traceback.print_exc()
        return False

def test_bandwidth_controller():
    """اختبار وحدة التحكم في النطاق الترددي"""
    print("\n🚦 اختبار التحكم في النطاق الترددي...")
    
    try:
        from bandwidth_controller import BandwidthController
        
        controller = BandwidthController()
        
        # فحص صلاحيات المدير
        if controller.is_admin:
            print("  ✅ صلاحيات المدير متوفرة")
        else:
            print("  ⚠️ صلاحيات المدير غير متوفرة - بعض الميزات قد لا تعمل")
        
        # اختبار الحصول على إحصائيات الشبكة
        stats = controller.get_network_statistics()
        if stats and 'bytes_sent' in stats:
            print("  ✅ إحصائيات الشبكة")
        else:
            print("  ❌ فشل في الحصول على إحصائيات الشبكة")
            return False
        
        # اختبار قائمة الأجهزة المحدودة
        limited_devices = controller.get_all_limited_devices()
        if isinstance(limited_devices, dict):
            print("  ✅ قائمة الأجهزة المحدودة")
        else:
            print("  ❌ خطأ في قائمة الأجهزة المحدودة")
            return False
        
        print("  ✅ التحكم في النطاق الترددي يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في التحكم في النطاق الترددي: {e}")
        traceback.print_exc()
        return False

def test_logger():
    """اختبار نظام السجلات"""
    print("\n📝 اختبار نظام السجلات...")
    
    try:
        from logger import NetworkLogger
        
        logger = NetworkLogger("test_logs")
        
        # اختبار أنواع مختلفة من السجلات
        logger.log_info("اختبار رسالة معلوماتية")
        logger.log_warning("اختبار تحذير")
        logger.log_error("اختبار خطأ")
        
        # اختبار السجلات المتخصصة
        logger.log_network_scan(5, 2.5)
        logger.log_device_blocked("***********00", "AA:BB:CC:DD:EE:FF")
        
        # اختبار الحصول على السجلات الحديثة
        recent_logs = logger.get_recent_logs(10)
        if recent_logs and len(recent_logs) > 0:
            print(f"  ✅ السجلات الحديثة ({len(recent_logs)} سجل)")
        else:
            print("  ❌ فشل في الحصول على السجلات الحديثة")
            return False
        
        # تنظيف مجلد الاختبار
        try:
            import shutil
            shutil.rmtree("test_logs")
        except:
            pass
        
        print("  ✅ نظام السجلات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في نظام السجلات: {e}")
        traceback.print_exc()
        return False

def test_gui_components():
    """اختبار مكونات الواجهة الرسومية"""
    print("\n🎨 اختبار مكونات الواجهة الرسومية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        import sys
        
        # إنشاء تطبيق مؤقت للاختبار
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        
        # اختبار استيراد المكونات
        from gui_main import DeviceTableWidget, DeviceSettingsDialog, MainWindow
        
        print("  ✅ استيراد مكونات الواجهة")
        
        # اختبار إنشاء جدول الأجهزة
        table = DeviceTableWidget()
        if table.columnCount() > 0:
            print("  ✅ جدول الأجهزة")
        else:
            print("  ❌ جدول الأجهزة")
            return False
        
        print("  ✅ مكونات الواجهة الرسومية تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في مكونات الواجهة الرسومية: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار شامل لمدير الشبكة المتقدم")
    print("🔬 Network Manager Pro - Comprehensive Test")
    print("=" * 70)
    print(f"📅 وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print(f"💻 نظام التشغيل: {os.name}")
    print("=" * 70)
    
    # قائمة الاختبارات
    tests = [
        ("استيراد المكتبات", test_imports),
        ("قاعدة البيانات", test_database),
        ("فحص الشبكة", test_network_scanner),
        ("ARP Spoofer", test_arp_spoofer),
        ("التحكم في النطاق الترددي", test_bandwidth_controller),
        ("نظام السجلات", test_logger),
        ("مكونات الواجهة الرسومية", test_gui_components)
    ]
    
    # تشغيل الاختبارات
    passed_tests = 0
    failed_tests = []
    
    for test_name, test_function in tests:
        try:
            if test_function():
                passed_tests += 1
            else:
                failed_tests.append(test_name)
        except Exception as e:
            print(f"❌ خطأ غير متوقع في اختبار {test_name}: {e}")
            failed_tests.append(test_name)
    
    # النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار النهائية:")
    print(f"✅ اختبارات ناجحة: {passed_tests}/{len(tests)}")
    
    if failed_tests:
        print(f"❌ اختبارات فاشلة: {len(failed_tests)}")
        for test in failed_tests:
            print(f"   • {test}")
    
    # التوصيات
    print("\n💡 التوصيات:")
    if len(failed_tests) == 0:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام.")
        print("▶️ يمكنك تشغيل البرنامج باستخدام: python gui_main.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        if "استيراد المكتبات" in failed_tests:
            print("📦 تشغيل: python install_requirements.py لتثبيت المكتبات المفقودة")
        print("🔧 تأكد من تثبيت جميع المتطلبات وإعادة تشغيل الاختبار")
    
    print("\n📝 ملاحظات:")
    print("• تأكد من تشغيل البرنامج بصلاحيات المدير للحصول على جميع الميزات")
    print("• تأكد من تثبيت Npcap من https://nmap.org/npcap/")
    print("• جميع السجلات محفوظة في مجلد logs/")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
