"""
وحدة التحكم في النطاق الترددي - تحديد سرعة الإنترنت للأجهزة
Bandwidth Controller Module - Limit internet speed for devices
"""

import subprocess
import threading
import time
import psutil
import socket
from typing import Dict, List, Optional
import ctypes
import sys
import os

class BandwidthController:
    """فئة للتحكم في سرعة الإنترنت للأجهزة"""
    
    def __init__(self):
        """تهيئة وحدة التحكم في النطاق الترددي"""
        self.limited_devices = {}
        self.monitoring_threads = {}
        self.running = False
        
        # فحص إذا كان البرنامج يعمل بصلاحيات المدير
        self.is_admin = self._check_admin_privileges()
        
        if not self.is_admin:
            print("⚠️ تحذير: البرنامج لا يعمل بصلاحيات المدير. قد لا تعمل بعض الميزات.")
    
    def _check_admin_privileges(self) -> bool:
        """فحص إذا كان البرنامج يعمل بصلاحيات المدير"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def _run_as_admin(self):
        """إعادة تشغيل البرنامج بصلاحيات المدير"""
        if not self.is_admin:
            try:
                ctypes.windll.shell32.ShellExecuteW(
                    None, "runas", sys.executable, " ".join(sys.argv), None, 1
                )
                sys.exit(0)
            except:
                print("❌ فشل في الحصول على صلاحيات المدير")
    
    def _get_network_interfaces(self) -> List[str]:
        """الحصول على قائمة واجهات الشبكة"""
        try:
            interfaces = []
            for interface_name, interface_addresses in psutil.net_if_addrs().items():
                for address in interface_addresses:
                    if address.family == socket.AF_INET:
                        interfaces.append(interface_name)
                        break
            return interfaces
        except Exception as e:
            print(f"❌ خطأ في الحصول على واجهات الشبكة: {e}")
            return []
    
    def _create_qos_policy(self, device_ip: str, download_limit: int, upload_limit: int) -> bool:
        """
        إنشاء سياسة QoS لتحديد سرعة جهاز معين
        Create QoS policy to limit device speed
        """
        try:
            if not self.is_admin:
                print("❌ مطلوب صلاحيات المدير لإنشاء سياسات QoS")
                return False
            
            # استخدام netsh لإنشاء سياسة QoS
            policy_name = f"Limit_{device_ip.replace('.', '_')}"
            
            # حذف السياسة إذا كانت موجودة
            subprocess.run([
                'netsh', 'qos', 'policy', 'delete', 'policy', policy_name
            ], capture_output=True, text=True)
            
            # إنشاء سياسة جديدة للتحميل (Download)
            if download_limit > 0:
                download_cmd = [
                    'netsh', 'qos', 'policy', 'add', 'policy',
                    f"{policy_name}_download",
                    'srcaddr', device_ip,
                    'throttleoutbound', str(download_limit * 1024)  # تحويل إلى bytes
                ]
                
                result = subprocess.run(download_cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    print(f"❌ فشل في إنشاء سياسة التحميل: {result.stderr}")
            
            # إنشاء سياسة جديدة للرفع (Upload)
            if upload_limit > 0:
                upload_cmd = [
                    'netsh', 'qos', 'policy', 'add', 'policy',
                    f"{policy_name}_upload",
                    'dstaddr', device_ip,
                    'throttleinbound', str(upload_limit * 1024)  # تحويل إلى bytes
                ]
                
                result = subprocess.run(upload_cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    print(f"❌ فشل في إنشاء سياسة الرفع: {result.stderr}")
            
            print(f"✅ تم إنشاء سياسة QoS للجهاز {device_ip}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء سياسة QoS: {e}")
            return False
    
    def _remove_qos_policy(self, device_ip: str) -> bool:
        """حذف سياسة QoS لجهاز معين"""
        try:
            if not self.is_admin:
                return False
            
            policy_name = f"Limit_{device_ip.replace('.', '_')}"
            
            # حذف سياسات التحميل والرفع
            for suffix in ['_download', '_upload']:
                subprocess.run([
                    'netsh', 'qos', 'policy', 'delete', 'policy', f"{policy_name}{suffix}"
                ], capture_output=True, text=True)
            
            print(f"✅ تم حذف سياسة QoS للجهاز {device_ip}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حذف سياسة QoS: {e}")
            return False
    
    def _monitor_device_traffic(self, device_ip: str):
        """مراقبة حركة البيانات لجهاز معين"""
        try:
            while self.running and device_ip in self.limited_devices:
                # هنا يمكن إضافة مراقبة فعلية لحركة البيانات
                # باستخدام مكتبات مثل psutil أو WinDivert
                
                # للآن، سنقوم بمراقبة بسيطة
                time.sleep(5)
                
                # يمكن إضافة إحصائيات حقيقية هنا
                if device_ip in self.limited_devices:
                    limits = self.limited_devices[device_ip]
                    print(f"📊 مراقبة الجهاز {device_ip} - حد التحميل: {limits['download']} KB/s, حد الرفع: {limits['upload']} KB/s")
                
        except Exception as e:
            print(f"❌ خطأ في مراقبة الجهاز {device_ip}: {e}")
    
    def limit_device_bandwidth(self, device_ip: str, download_limit: int, upload_limit: int) -> bool:
        """
        تحديد سرعة الإنترنت لجهاز معين
        
        Args:
            device_ip: عنوان IP للجهاز
            download_limit: حد التحميل بـ KB/s
            upload_limit: حد الرفع بـ KB/s
        """
        try:
            # حفظ إعدادات الجهاز
            self.limited_devices[device_ip] = {
                'download': download_limit,
                'upload': upload_limit,
                'start_time': time.time()
            }
            
            # إنشاء سياسة QoS
            success = self._create_qos_policy(device_ip, download_limit, upload_limit)
            
            if success:
                # بدء مراقبة الجهاز
                self.running = True
                monitor_thread = threading.Thread(
                    target=self._monitor_device_traffic,
                    args=(device_ip,),
                    daemon=True
                )
                monitor_thread.start()
                self.monitoring_threads[device_ip] = monitor_thread
                
                print(f"🚦 تم تحديد سرعة الجهاز {device_ip} - تحميل: {download_limit} KB/s, رفع: {upload_limit} KB/s")
                return True
            else:
                # إزالة من القائمة إذا فشل
                if device_ip in self.limited_devices:
                    del self.limited_devices[device_ip]
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحديد سرعة الجهاز {device_ip}: {e}")
            return False
    
    def remove_device_limit(self, device_ip: str) -> bool:
        """إزالة تحديد السرعة من جهاز معين"""
        try:
            if device_ip not in self.limited_devices:
                print(f"⚠️ الجهاز {device_ip} غير محدود السرعة")
                return True
            
            # حذف سياسة QoS
            self._remove_qos_policy(device_ip)
            
            # إزالة من القوائم
            if device_ip in self.limited_devices:
                del self.limited_devices[device_ip]
            
            if device_ip in self.monitoring_threads:
                del self.monitoring_threads[device_ip]
            
            print(f"✅ تم إزالة تحديد السرعة من الجهاز {device_ip}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إزالة تحديد السرعة: {e}")
            return False
    
    def get_device_limits(self, device_ip: str) -> Optional[Dict]:
        """الحصول على حدود السرعة لجهاز معين"""
        return self.limited_devices.get(device_ip)
    
    def get_all_limited_devices(self) -> Dict:
        """الحصول على جميع الأجهزة المحدودة السرعة"""
        return self.limited_devices.copy()
    
    def remove_all_limits(self):
        """إزالة جميع تحديدات السرعة"""
        device_ips = list(self.limited_devices.keys())
        
        for device_ip in device_ips:
            self.remove_device_limit(device_ip)
        
        self.limited_devices.clear()
        self.monitoring_threads.clear()
        
        print("✅ تم إزالة جميع تحديدات السرعة")
    
    def get_network_statistics(self) -> Dict:
        """الحصول على إحصائيات الشبكة العامة"""
        try:
            stats = psutil.net_io_counters()
            return {
                'bytes_sent': stats.bytes_sent,
                'bytes_recv': stats.bytes_recv,
                'packets_sent': stats.packets_sent,
                'packets_recv': stats.packets_recv,
                'errin': stats.errin,
                'errout': stats.errout,
                'dropin': stats.dropin,
                'dropout': stats.dropout
            }
        except Exception as e:
            print(f"❌ خطأ في الحصول على إحصائيات الشبكة: {e}")
            return {}
    
    def stop_all_monitoring(self):
        """إيقاف جميع عمليات المراقبة"""
        self.running = False
        self.remove_all_limits()
        print("🛑 تم إيقاف جميع عمليات مراقبة النطاق الترددي")

# دالة مساعدة للاختبار
def test_bandwidth_controller():
    """اختبار وحدة التحكم في النطاق الترددي"""
    print("🧪 اختبار Bandwidth Controller...")
    
    controller = BandwidthController()
    
    print(f"👤 صلاحيات المدير: {'نعم' if controller.is_admin else 'لا'}")
    print(f"📊 إحصائيات الشبكة: {controller.get_network_statistics()}")
    
    # اختبار تحديد سرعة جهاز وهمي
    test_ip = "*************"
    print(f"🧪 اختبار تحديد سرعة الجهاز {test_ip}...")
    
    # ملاحظة: هذا سيفشل بدون صلاحيات المدير
    result = controller.limit_device_bandwidth(test_ip, 100, 50)  # 100 KB/s تحميل, 50 KB/s رفع
    print(f"نتيجة الاختبار: {'نجح' if result else 'فشل'}")

if __name__ == "__main__":
    test_bandwidth_controller()
