@echo off
chcp 65001 >nul
title بناء ملف EXE - Network Manager Pro

echo.
echo 🏗️ بناء ملف EXE لمدير الشبكة المتقدم
echo 🔨 Building EXE for Network Manager Pro
echo.
echo ⏳ هذه العملية قد تستغرق عدة دقائق...
echo 📦 سيتم تحميل وتثبيت جميع المتطلبات تلقائياً
echo.

:: فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version
echo.

:: تشغيل سكريبت البناء
echo 🔄 بدء عملية البناء...
python build_exe.py

:: فحص النتيجة
if errorlevel 1 (
    echo.
    echo ❌ فشل في بناء EXE
    echo 🔧 تحقق من الأخطاء أعلاه
) else (
    echo.
    echo 🎉 تم بناء EXE بنجاح!
    echo 📁 تحقق من مجلد NetworkManagerPro_Portable
)

echo.
pause
