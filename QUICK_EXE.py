"""
بناء سريع لملف EXE
Quick EXE Builder
"""

import os
import sys
import subprocess
import shutil

def run_command(command, description):
    """تشغيل أمر مع عرض الوصف"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - نجح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - فشل")
        print(f"خطأ: {e.stderr}")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    requirements = [
        "pip install --upgrade pip",
        "pip install PyQt5==5.15.9",
        "pip install scapy==2.5.0", 
        "pip install psutil==5.9.5",
        "pip install netifaces==0.11.0",
        "pip install pyinstaller"
    ]
    
    for req in requirements:
        if not run_command(req, f"تثبيت {req.split()[-1]}"):
            return False
    
    return True

def build_exe():
    """بناء ملف EXE"""
    command = 'pyinstaller --onefile --windowed --name="NetworkManagerPro" --uac-admin gui_main.py'
    return run_command(command, "بناء ملف EXE")

def create_package():
    """إنشاء حزمة التوزيع"""
    print("📦 إنشاء حزمة التوزيع...")
    
    # إنشاء مجلد التوزيع
    package_dir = "NetworkManagerPro_Ready"
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    
    os.makedirs(package_dir)
    os.makedirs(f"{package_dir}/logs")
    os.makedirs(f"{package_dir}/backups")
    
    # نسخ ملف EXE
    exe_source = "dist/NetworkManagerPro.exe"
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, package_dir)
        print("✅ تم نسخ ملف EXE")
    else:
        print("❌ ملف EXE غير موجود")
        return False
    
    # إنشاء ملف التشغيل
    launcher_content = '''@echo off
chcp 65001 >nul
title مدير الشبكة المتقدم

echo.
echo 🚀 مدير الشبكة المتقدم
echo =======================
echo.
echo 🔐 تشغيل البرنامج بصلاحيات المدير...
powershell -Command "Start-Process '%~dp0NetworkManagerPro.exe' -Verb RunAs"
echo.
echo ✅ تم تشغيل البرنامج
pause
'''
    
    with open(f"{package_dir}/تشغيل_البرنامج.bat", 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    # إنشاء ملف README
    readme_content = '''# مدير الشبكة المتقدم - Network Manager Pro

## طريقة التشغيل:
1. اضغط دبل كليك على "تشغيل_البرنامج.bat"
2. أو اضغط يمين على "NetworkManagerPro.exe" واختر "Run as administrator"

## متطلبات مهمة:
- يجب تثبيت Npcap من: https://nmap.org/npcap/
- تأكد من تحديد خيار "Install Npcap in WinPcap API-compatible Mode"
- البرنامج يحتاج صلاحيات المدير للعمل

## الميزات:
- فحص الشبكة واكتشاف الأجهزة
- حجب الأجهزة من الإنترنت (ARP Spoofing)
- التحكم في سرعة الإنترنت
- حفظ الإعدادات تلقائياً حسب MAC Address
- واجهة عربية سهلة الاستخدام

## الأمان:
- جميع العمليات محلية على الشبكة المحلية فقط
- لا يتم إرسال أي بيانات خارج شبكتك
- استخدم البرنامج بمسؤولية على شبكتك فقط

## استكشاف الأخطاء:
- تأكد من تثبيت Npcap
- تأكد من تشغيل البرنامج كمدير
- راجع ملفات السجلات في مجلد logs/

الإصدار: 1.0.0
التوافق: Windows 10/11
'''
    
    with open(f"{package_dir}/README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء حزمة التوزيع")
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🚀 بناء سريع لمدير الشبكة المتقدم")
    print("=" * 50)
    
    # فحص Python
    print(f"🐍 Python: {sys.version}")
    
    # تثبيت المتطلبات
    print("\n📦 تثبيت المتطلبات...")
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return 1
    
    # بناء EXE
    print("\n🔨 بناء ملف EXE...")
    if not build_exe():
        print("❌ فشل في بناء EXE")
        return 1
    
    # إنشاء الحزمة
    print("\n📦 إنشاء حزمة التوزيع...")
    if not create_package():
        print("❌ فشل في إنشاء الحزمة")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 تم بناء البرنامج بنجاح!")
    print("📁 البرنامج موجود في مجلد: NetworkManagerPro_Ready")
    print("📋 اقرأ ملف README.txt للتعليمات")
    print("⚠️ لا تنس تثبيت Npcap من: https://nmap.org/npcap/")
    print("=" * 50)
    
    # فتح مجلد النتائج
    try:
        os.startfile("NetworkManagerPro_Ready")
        print("✅ تم فتح مجلد البرنامج")
    except:
        print("📁 افتح مجلد NetworkManagerPro_Ready يدوياً")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\nاضغط Enter للخروج...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
