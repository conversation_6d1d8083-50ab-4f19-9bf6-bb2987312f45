@echo off
chcp 65001 >nul
title مدير الشبكة المتقدم - Network Manager Pro

echo ================================================================
echo 🚀 مدير الشبكة المتقدم - Network Manager Pro
echo 📡 Advanced Network Management Tool
echo ================================================================
echo.

:: فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 📥 يرجى تحميل وتثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

:: فحص وجود الملفات المطلوبة
if not exist "gui_main.py" (
    echo ❌ ملف gui_main.py غير موجود
    echo 📁 تأكد من وجود جميع ملفات البرنامج في نفس المجلد
    pause
    exit /b 1
)

echo ✅ تم العثور على ملفات البرنامج

:: فحص صلاحيات المدير
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: البرنامج لا يعمل بصلاحيات المدير
    echo 🔧 بعض الميزات قد لا تعمل بشكل كامل
    echo.
    echo هل تريد المتابعة؟ (y/n)
    set /p choice=
    if /i not "%choice%"=="y" exit /b 1
) else (
    echo ✅ البرنامج يعمل بصلاحيات المدير
)

echo.
echo 🔄 بدء تشغيل البرنامج...
echo ================================================================
echo.

:: تشغيل البرنامج
python gui_main.py

:: في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo ================================================================
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo 💡 اقتراحات لحل المشكلة:
    echo 1. تشغيل: python install_requirements.py
    echo 2. تأكد من تثبيت Npcap من: https://nmap.org/npcap/
    echo 3. تشغيل البرنامج بصلاحيات المدير
    echo 4. فحص ملف السجلات في مجلد logs/
    echo.
    echo 🧪 لاختبار المكونات: python test_all_modules.py
    echo ================================================================
    pause
)
