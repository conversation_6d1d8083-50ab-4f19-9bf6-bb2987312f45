#!/usr/bin/env python3
"""
Simple EXE Builder for Network Manager Pro
بناء بسيط لمدير الشبكة المتقدم
"""

import os
import sys
import subprocess
import shutil
import time

def print_step(step, description):
    """طباعة خطوة مع تنسيق"""
    print(f"\n[{step}] {description}")
    print("-" * 50)

def run_cmd(command, description):
    """تشغيل أمر مع معالجة الأخطاء"""
    print(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"SUCCESS: {description}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {description}")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return False
    except Exception as e:
        print(f"EXCEPTION: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("Network Manager Pro - EXE Builder")
    print("مدير الشبكة المتقدم - بناء ملف EXE")
    print("=" * 60)
    
    # Step 1: Check Python
    print_step("1", "Checking Python installation")
    print(f"Python version: {sys.version}")
    
    # Step 2: Install requirements
    print_step("2", "Installing Python packages")
    packages = [
        "pip install --upgrade pip",
        "pip install PyQt5==5.15.9",
        "pip install scapy==2.5.0",
        "pip install psutil==5.9.5", 
        "pip install netifaces==0.11.0",
        "pip install pyinstaller"
    ]
    
    for pkg in packages:
        if not run_cmd(pkg, f"Installing {pkg.split()[-1]}"):
            print("Failed to install packages. Exiting.")
            return 1
    
    # Step 3: Build EXE
    print_step("3", "Building EXE file")
    build_cmd = 'pyinstaller --onefile --windowed --name=NetworkManagerPro --uac-admin gui_main.py'
    
    if not run_cmd(build_cmd, "Building EXE with PyInstaller"):
        print("Failed to build EXE. Exiting.")
        return 1
    
    # Step 4: Create package
    print_step("4", "Creating distribution package")
    
    # Clean and create directory
    package_dir = "NetworkManagerPro_Final"
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    
    os.makedirs(package_dir)
    os.makedirs(f"{package_dir}/logs")
    os.makedirs(f"{package_dir}/backups")
    
    # Copy EXE
    exe_path = "dist/NetworkManagerPro.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, package_dir)
        print("EXE copied successfully")
    else:
        print("ERROR: EXE file not found!")
        return 1
    
    # Create launcher script
    launcher_content = '''@echo off
title Network Manager Pro

echo.
echo Network Manager Pro - Advanced Network Manager
echo ==============================================
echo.
echo Starting with Administrator privileges...
powershell -Command "Start-Process '%~dp0NetworkManagerPro.exe' -Verb RunAs"
echo.
echo Program started successfully
pause
'''
    
    with open(f"{package_dir}/RUN_AS_ADMIN.bat", 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    # Create README
    readme_content = '''Network Manager Pro - Advanced Network Management Tool
=======================================================

HOW TO RUN:
1. Double-click on "RUN_AS_ADMIN.bat"
2. OR right-click on "NetworkManagerPro.exe" and select "Run as administrator"

IMPORTANT REQUIREMENTS:
- Install Npcap from: https://nmap.org/npcap/
- Make sure to check "Install Npcap in WinPcap API-compatible Mode"
- Administrator privileges required

FEATURES:
- Network scanning and device discovery
- Block devices from internet access (ARP Spoofing)
- Bandwidth control and speed limiting
- Automatic settings save by MAC address
- Arabic interface with English support

SECURITY:
- All operations are local network only
- No data sent outside your network
- Use responsibly on your own network only

TROUBLESHOOTING:
- Make sure Npcap is installed
- Make sure to run as Administrator
- Check log files in logs/ folder

Version: 1.0.0
Compatible: Windows 10/11
'''
    
    with open(f"{package_dir}/README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("Package created successfully")
    
    # Step 5: Final message
    print_step("5", "Build completed!")
    print("=" * 60)
    print("BUILD COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print(f"Program location: {package_dir}/")
    print("Read README.txt for instructions")
    print("Don't forget to install Npcap from: https://nmap.org/npcap/")
    print("=" * 60)
    
    # Open folder
    try:
        if sys.platform == "win32":
            os.startfile(package_dir)
        else:
            subprocess.run(["xdg-open", package_dir])
        print("Results folder opened")
    except:
        print(f"Please open the '{package_dir}' folder manually")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\nPress Enter to exit...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nBuild cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
