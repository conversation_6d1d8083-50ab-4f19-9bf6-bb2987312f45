"""
بناء ملف EXE للبرنامج مع جميع المتطلبات
Build EXE file with all dependencies included
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("📦 تثبيت PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ تم تثبيت PyInstaller بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت PyInstaller")
        return False

def install_all_requirements():
    """تثبيت جميع المتطلبات"""
    print("📦 تثبيت جميع المتطلبات...")
    
    requirements = [
        "PyQt5==5.15.9",
        "scapy==2.5.0", 
        "psutil==5.9.5",
        "netifaces==0.11.0",
        "pyinstaller"
    ]
    
    for req in requirements:
        try:
            print(f"  📥 تثبيت {req}...")
            subprocess.run([sys.executable, "-m", "pip", "install", req], check=True)
            print(f"  ✅ تم تثبيت {req}")
        except subprocess.CalledProcessError:
            print(f"  ❌ فشل في تثبيت {req}")
            return False
    
    print("✅ تم تثبيت جميع المتطلبات")
    return True

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['gui_main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('database.py', '.'),
        ('network_scanner.py', '.'),
        ('arp_spoofer.py', '.'),
        ('bandwidth_controller.py', '.'),
        ('logger.py', '.'),
        ('config.py', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'scapy.all',
        'psutil',
        'netifaces',
        'sqlite3',
        'threading',
        'subprocess',
        'ctypes',
        'socket',
        'ipaddress',
        'time',
        'datetime',
        'json',
        'os',
        'sys',
        're'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='NetworkManagerPro',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
    uac_admin=True,  # طلب صلاحيات المدير تلقائياً
)
'''
    
    with open('NetworkManagerPro.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف .spec")

def create_version_info():
    """إنشاء ملف معلومات الإصدار"""
    version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Network Manager Pro'),
        StringStruct(u'FileDescription', u'Advanced Network Management Tool'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'NetworkManagerPro'),
        StringStruct(u'LegalCopyright', u'© 2024 Network Manager Pro'),
        StringStruct(u'OriginalFilename', u'NetworkManagerPro.exe'),
        StringStruct(u'ProductName', u'Network Manager Pro'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ تم إنشاء ملف معلومات الإصدار")

def create_icon():
    """إنشاء أيقونة بسيطة للبرنامج"""
    # سنقوم بإنشاء أيقونة بسيطة أو استخدام أيقونة افتراضية
    print("📝 ملاحظة: يمكنك إضافة ملف icon.ico لأيقونة مخصصة")

def build_exe():
    """بناء ملف EXE"""
    print("🔨 بناء ملف EXE...")
    
    try:
        # بناء البرنامج
        subprocess.run([
            sys.executable, "-m", "PyInstaller", 
            "--clean",
            "NetworkManagerPro.spec"
        ], check=True)
        
        print("✅ تم بناء ملف EXE بنجاح!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بناء ملف EXE: {e}")
        return False

def create_portable_package():
    """إنشاء حزمة محمولة كاملة"""
    print("📦 إنشاء الحزمة المحمولة...")
    
    # إنشاء مجلد الحزمة
    package_dir = "NetworkManagerPro_Portable"
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    
    os.makedirs(package_dir)
    
    # نسخ ملف EXE
    exe_source = "dist/NetworkManagerPro.exe"
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, package_dir)
        print("✅ تم نسخ ملف EXE")
    else:
        print("❌ ملف EXE غير موجود")
        return False
    
    # إنشاء ملفات مساعدة
    create_readme_for_exe(package_dir)
    create_run_as_admin_bat(package_dir)
    create_npcap_installer_info(package_dir)
    
    # إنشاء مجلدات فرعية
    os.makedirs(f"{package_dir}/logs", exist_ok=True)
    os.makedirs(f"{package_dir}/backups", exist_ok=True)
    
    print(f"✅ تم إنشاء الحزمة المحمولة في مجلد: {package_dir}")
    return True

def create_readme_for_exe(package_dir):
    """إنشاء ملف README للحزمة المحمولة"""
    readme_content = """# مدير الشبكة المتقدم - Network Manager Pro

## 🚀 طريقة التشغيل:

### الطريقة الأولى (الأسهل):
1. اضغط دبل كليك على `Run_as_Admin.bat`
2. سيتم تشغيل البرنامج بصلاحيات المدير تلقائياً

### الطريقة الثانية:
1. اضغط يمين على `NetworkManagerPro.exe`
2. اختر "Run as administrator"

## ⚠️ متطلبات مهمة:

### 1. Npcap (مطلوب):
- يجب تثبيت Npcap من: https://nmap.org/npcap/
- تأكد من تحديد خيار "Install Npcap in WinPcap API-compatible Mode"
- ملف التثبيت موجود في مجلد `npcap/` (إذا كان متوفراً)

### 2. صلاحيات المدير:
- البرنامج يحتاج صلاحيات المدير للعمل بشكل كامل
- استخدم `Run_as_Admin.bat` للتشغيل التلقائي بصلاحيات المدير

## 📁 محتويات الحزمة:

- `NetworkManagerPro.exe` - البرنامج الرئيسي
- `Run_as_Admin.bat` - تشغيل بصلاحيات المدير
- `README.txt` - هذا الملف
- `logs/` - مجلد السجلات (يتم إنشاؤه تلقائياً)
- `backups/` - مجلد النسخ الاحتياطية

## 🎯 الميزات:

✅ فحص الشبكة واكتشاف الأجهزة
✅ حجب الأجهزة من الإنترنت (ARP Spoofing)
✅ التحكم في سرعة الإنترنت
✅ حفظ الإعدادات تلقائياً حسب MAC Address
✅ نظام سجلات متقدم
✅ واجهة عربية سهلة الاستخدام

## 🔒 الأمان:
- جميع العمليات محلية على الشبكة المحلية فقط
- لا يتم إرسال أي بيانات خارج شبكتك
- استخدم البرنامج بمسؤولية على شبكتك فقط

## 📞 الدعم:
- جميع السجلات محفوظة في مجلد `logs/`
- في حالة وجود مشاكل، راجع ملفات السجلات

---
الإصدار: 1.0.0
التوافق: Windows 10/11
"""
    
    with open(f"{package_dir}/README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)

def create_run_as_admin_bat(package_dir):
    """إنشاء ملف bat للتشغيل بصلاحيات المدير"""
    bat_content = '''@echo off
chcp 65001 >nul
title مدير الشبكة المتقدم - Network Manager Pro

echo.
echo 🚀 مدير الشبكة المتقدم - Network Manager Pro
echo 📡 Advanced Network Management Tool
echo.
echo 🔐 تشغيل البرنامج بصلاحيات المدير...
echo.

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️ طلب صلاحيات المدير...
    powershell -Command "Start-Process '%~dp0NetworkManagerPro.exe' -Verb RunAs"
) else (
    echo ✅ تم التحقق من صلاحيات المدير
    start "" "%~dp0NetworkManagerPro.exe"
)

echo.
echo ✅ تم تشغيل البرنامج
echo 📝 إذا لم يظهر البرنامج، تأكد من تثبيت Npcap
echo 🌐 رابط تحميل Npcap: https://nmap.org/npcap/
echo.
pause
'''
    
    with open(f"{package_dir}/Run_as_Admin.bat", 'w', encoding='utf-8') as f:
        f.write(bat_content)

def create_npcap_installer_info(package_dir):
    """إنشاء معلومات تثبيت Npcap"""
    npcap_info = """# تثبيت Npcap - مطلوب للبرنامج

## ما هو Npcap؟
Npcap هو مكتبة مطلوبة للبرنامج للتعامل مع حزم الشبكة على Windows.

## طريقة التثبيت:

1. اذهب إلى: https://nmap.org/npcap/
2. حمل أحدث إصدار من Npcap
3. شغل ملف التثبيت كـ Administrator
4. **مهم جداً**: تأكد من تحديد خيار:
   "Install Npcap in WinPcap API-compatible Mode"

## بدون Npcap:
- البرنامج لن يعمل بشكل صحيح
- لن تتمكن من فحص الشبكة أو حجب الأجهزة

## ملاحظة:
إذا كان لديك WinPcap مثبت مسبقاً، قم بإلغاء تثبيته أولاً ثم ثبت Npcap.
"""
    
    npcap_dir = f"{package_dir}/npcap_info"
    os.makedirs(npcap_dir, exist_ok=True)
    
    with open(f"{npcap_dir}/تثبيت_Npcap.txt", 'w', encoding='utf-8') as f:
        f.write(npcap_info)

def download_npcap():
    """محاولة تحميل Npcap (اختياري)"""
    print("📥 ملاحظة: يمكنك تحميل Npcap يدوياً من:")
    print("   https://nmap.org/npcap/")
    print("   وإضافته لمجلد الحزمة")

def main():
    """الدالة الرئيسية لبناء EXE"""
    print("=" * 60)
    print("🏗️ بناء ملف EXE لمدير الشبكة المتقدم")
    print("🔨 Building EXE for Network Manager Pro")
    print("=" * 60)
    
    # فحص Python
    print(f"🐍 Python: {sys.version}")
    
    # تثبيت المتطلبات
    if not install_all_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return
    
    # إنشاء الملفات المساعدة
    create_spec_file()
    create_version_info()
    create_icon()
    
    # بناء EXE
    if not build_exe():
        print("❌ فشل في بناء EXE")
        return
    
    # إنشاء الحزمة المحمولة
    if not create_portable_package():
        print("❌ فشل في إنشاء الحزمة المحمولة")
        return
    
    # معلومات إضافية
    download_npcap()
    
    print("\n" + "=" * 60)
    print("🎉 تم بناء البرنامج بنجاح!")
    print("📁 الحزمة المحمولة موجودة في: NetworkManagerPro_Portable/")
    print("📝 اقرأ ملف README.txt في الحزمة للتعليمات")
    print("⚠️ لا تنس تثبيت Npcap من: https://nmap.org/npcap/")
    print("=" * 60)

if __name__ == "__main__":
    main()
