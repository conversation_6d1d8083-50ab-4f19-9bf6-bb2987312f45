@echo off
chcp 65001 >nul
title بناء الحزمة الكاملة - Network Manager Pro

echo.
echo 🎁 بناء الحزمة الكاملة - Network Manager Pro
echo 📦 Complete Package Builder
echo.
echo ⏳ هذه العملية ستقوم بـ:
echo   📥 تحميل وتثبيت جميع المتطلبات
echo   🏗️ بناء ملف EXE
echo   📦 تحميل Npcap
echo   🎁 إنشاء حزمة كاملة جاهزة للتوزيع
echo.
echo ⚠️ تأكد من اتصالك بالإنترنت
echo ⏱️ قد تستغرق العملية 10-15 دقيقة
echo.

pause

:: فحص Python
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo 💡 تأكد من تحديد "Add Python to PATH"
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version
echo.

:: بناء الحزمة الكاملة
echo 🚀 بدء بناء الحزمة الكاملة...
echo ================================
python build_complete_package.py

:: فحص النتيجة
if errorlevel 1 (
    echo.
    echo ❌ فشل في بناء الحزمة
    echo 🔧 راجع الأخطاء أعلاه
    echo.
    echo 💡 حلول مقترحة:
    echo   1. تأكد من اتصالك بالإنترنت
    echo   2. شغل البرنامج كمدير
    echo   3. تأكد من وجود مساحة كافية على القرص
    echo.
) else (
    echo.
    echo 🎉 تم بناء الحزمة بنجاح!
    echo.
    echo 📁 تحقق من:
    echo   • مجلد NetworkManagerPro_Complete/
    echo   • ملف NetworkManagerPro_Complete.zip
    echo.
    echo 🚀 الحزمة جاهزة للتوزيع!
    echo.
    
    :: فتح مجلد النتائج
    if exist "NetworkManagerPro_Complete" (
        echo 📂 فتح مجلد النتائج...
        start "" "NetworkManagerPro_Complete"
    )
)

echo.
pause
