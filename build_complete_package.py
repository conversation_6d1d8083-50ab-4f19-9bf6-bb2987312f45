"""
بناء حزمة كاملة مع جميع المتطلبات
Build complete package with all requirements
"""

import os
import sys
import subprocess
import shutil
import zipfile
from pathlib import Path

def create_complete_package():
    """إنشاء حزمة كاملة مع كل شيء"""
    print("📦 إنشاء الحزمة الكاملة...")
    
    package_name = "NetworkManagerPro_Complete"
    
    # حذف الحزمة القديمة إن وجدت
    if os.path.exists(package_name):
        shutil.rmtree(package_name)
    
    # إنشاء مجلد الحزمة
    os.makedirs(package_name)
    
    # نسخ ملف EXE إذا كان موجود
    exe_path = "NetworkManagerPro_Portable/NetworkManagerPro.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, package_name)
        print("✅ تم نسخ ملف EXE")
    else:
        print("⚠️ ملف EXE غير موجود - سيتم إنشاؤه")
        # بناء EXE أولاً
        subprocess.run([sys.executable, "build_exe.py"])
        if os.path.exists(exe_path):
            shutil.copy2(exe_path, package_name)
    
    # نسخ Npcap إذا كان موجود
    if os.path.exists("npcap"):
        shutil.copytree("npcap", f"{package_name}/npcap")
        print("✅ تم نسخ Npcap")
    else:
        print("📥 تحميل Npcap...")
        subprocess.run([sys.executable, "download_npcap.py"])
        if os.path.exists("npcap"):
            shutil.copytree("npcap", f"{package_name}/npcap")
    
    # إنشاء ملفات التشغيل
    create_launcher_files(package_name)
    
    # إنشاء دليل المستخدم
    create_user_guide(package_name)
    
    # إنشاء مجلدات فرعية
    os.makedirs(f"{package_name}/logs", exist_ok=True)
    os.makedirs(f"{package_name}/backups", exist_ok=True)
    
    print(f"✅ تم إنشاء الحزمة الكاملة: {package_name}/")
    return package_name

def create_launcher_files(package_dir):
    """إنشاء ملفات التشغيل"""
    
    # ملف التشغيل الرئيسي
    main_launcher = f'''@echo off
chcp 65001 >nul
title مدير الشبكة المتقدم - Network Manager Pro

echo.
echo 🚀 مدير الشبكة المتقدم - Network Manager Pro
echo 📡 Advanced Network Management Tool
echo.

:: فحص وجود Npcap
echo 🔍 فحص متطلبات النظام...

:: فحص Npcap
reg query "HKLM\\SOFTWARE\\Npcap" >nul 2>&1
if errorlevel 1 (
    echo ❌ Npcap غير مثبت
    echo.
    echo 📦 يجب تثبيت Npcap أولاً للبرنامج
    echo 🔧 سيتم فتح مثبت Npcap...
    echo.
    
    if exist "npcap\\npcap-installer.exe" (
        echo ▶️ تشغيل مثبت Npcap...
        start "" "npcap\\npcap-installer.exe"
        echo.
        echo ⚠️ بعد تثبيت Npcap، أعد تشغيل هذا الملف
        pause
        exit /b 1
    ) else (
        echo ❌ مثبت Npcap غير موجود
        echo 🌐 يرجى تحميله من: https://nmap.org/npcap/
        echo 💡 تأكد من تحديد خيار "WinPcap API-compatible Mode"
        pause
        exit /b 1
    )
) else (
    echo ✅ Npcap مثبت
)

:: فحص صلاحيات المدير
net session >nul 2>&1
if errorlevel 1 (
    echo 🔐 طلب صلاحيات المدير...
    powershell -Command "Start-Process '%~dp0NetworkManagerPro.exe' -Verb RunAs"
) else (
    echo ✅ صلاحيات المدير متوفرة
    start "" "%~dp0NetworkManagerPro.exe"
)

echo.
echo ✅ تم تشغيل البرنامج
echo 📝 إذا واجهت مشاكل، راجع ملف "دليل_المستخدم.txt"
echo.
'''
    
    with open(f"{package_dir}/تشغيل_البرنامج.bat", 'w', encoding='utf-8') as f:
        f.write(main_launcher)
    
    # ملف تثبيت Npcap منفصل
    npcap_installer = f'''@echo off
chcp 65001 >nul
title تثبيت Npcap

echo.
echo 📦 تثبيت Npcap - مطلوب للبرنامج
echo.

if exist "npcap\\npcap-installer.exe" (
    echo ▶️ تشغيل مثبت Npcap...
    echo.
    echo ⚠️ ملاحظات مهمة:
    echo 1. تأكد من تحديد خيار "Install Npcap in WinPcap API-compatible Mode"
    echo 2. قد تحتاج لإعادة تشغيل الكمبيوتر بعد التثبيت
    echo.
    pause
    
    start "" "npcap\\npcap-installer.exe"
    
    echo.
    echo ✅ تم تشغيل مثبت Npcap
    echo 🔄 بعد انتهاء التثبيت، شغل "تشغيل_البرنامج.bat"
) else (
    echo ❌ مثبت Npcap غير موجود
    echo 🌐 يرجى تحميله من: https://nmap.org/npcap/
)

echo.
pause
'''
    
    with open(f"{package_dir}/تثبيت_Npcap.bat", 'w', encoding='utf-8') as f:
        f.write(npcap_installer)

def create_user_guide(package_dir):
    """إنشاء دليل المستخدم الشامل"""
    guide_content = """# دليل المستخدم - مدير الشبكة المتقدم

## 🚀 البدء السريع:

### الخطوة 1: تثبيت Npcap
1. اضغط دبل كليك على "تثبيت_Npcap.bat"
2. اتبع تعليمات التثبيت
3. **مهم جداً**: تأكد من تحديد خيار "Install Npcap in WinPcap API-compatible Mode"

### الخطوة 2: تشغيل البرنامج
1. اضغط دبل كليك على "تشغيل_البرنامج.bat"
2. سيتم طلب صلاحيات المدير - اضغط "نعم"
3. انتظر حتى يفتح البرنامج

## 📱 استخدام البرنامج:

### فحص الشبكة:
- البرنامج يفحص الشبكة تلقائياً كل 30 ثانية
- يمكنك الضغط على "فحص الشبكة" للفحص اليدوي
- ستظهر جميع الأجهزة المتصلة في الجدول

### حجب جهاز:
1. اختر الجهاز من الجدول
2. اضغط "حجب الجهاز"
3. سيتم قطع الإنترنت عن الجهاز فوراً

### تحديد سرعة جهاز:
1. اختر الجهاز من الجدول
2. اضغط "إعدادات الجهاز"
3. اختر "تحديد السرعة"
4. حدد سرعة التحميل والرفع
5. اضغط "موافق"

### إلغاء الحجب:
1. اختر الجهاز المحجوب
2. اضغط "إلغاء الحجب"
3. سيعود الإنترنت للجهاز

## 🔧 حل المشاكل الشائعة:

### المشكلة: "لا تظهر أي أجهزة"
الحل:
- تأكد من تثبيت Npcap بشكل صحيح
- تأكد من تشغيل البرنامج بصلاحيات المدير
- تأكد من اتصالك بالشبكة المحلية

### المشكلة: "فشل في حجب الجهاز"
الحل:
- تأكد من صلاحيات المدير
- تأكد من أن الجهاز متصل بنفس الشبكة
- جرب إعادة تشغيل البرنامج

### المشكلة: "البرنامج لا يفتح"
الحل:
- تأكد من تثبيت Npcap
- شغل البرنامج كمدير
- تحقق من ملفات السجلات في مجلد logs/

## 📊 الميزات المتقدمة:

### حفظ الإعدادات التلقائي:
- البرنامج يحفظ إعدادات كل جهاز تلقائياً
- عند إعادة اتصال الجهاز، يتم تطبيق الإعدادات المحفوظة
- الإعدادات محفوظة حسب MAC Address

### السجلات:
- جميع العمليات مسجلة في تبويب "السجلات"
- يمكنك تصدير السجلات لملف نصي
- السجلات محفوظة في مجلد logs/

### النسخ الاحتياطية:
- البرنامج ينشئ نسخ احتياطية تلقائياً
- النسخ محفوظة في مجلد backups/

## ⚠️ تحذيرات مهمة:

1. **استخدم البرنامج بمسؤولية**: فقط على شبكتك الخاصة
2. **لا تحجب الراوتر**: قد يقطع الإنترنت عن جميع الأجهزة
3. **احتفظ بنسخة احتياطية**: من إعداداتك المهمة
4. **تأكد من القوانين المحلية**: قبل استخدام البرنامج

## 📞 الدعم:

### ملفات السجلات:
- logs/network_manager.log - السجل الرئيسي
- logs/arp_spoofing.log - سجل حجب الأجهزة
- logs/bandwidth_control.log - سجل تحديد السرعة
- logs/errors.log - سجل الأخطاء

### معلومات النظام:
- الإصدار: 1.0.0
- التوافق: Windows 10/11
- المتطلبات: Npcap, صلاحيات المدير

---

## 🎯 نصائح للاستخدام الأمثل:

1. **فحص دوري**: راجع السجلات بانتظام
2. **نسخ احتياطية**: احتفظ بنسخة من مجلد backups/
3. **تحديثات**: تحقق من وجود تحديثات للبرنامج
4. **الأمان**: لا تشارك إعداداتك مع أشخاص غير موثوقين

---

*تم إنشاء هذا الدليل تلقائياً مع البرنامج*
"""
    
    with open(f"{package_dir}/دليل_المستخدم.txt", 'w', encoding='utf-8') as f:
        f.write(guide_content)

def create_zip_package(package_dir):
    """إنشاء ملف ZIP للحزمة"""
    zip_name = f"{package_dir}.zip"
    
    print(f"📦 إنشاء ملف ZIP: {zip_name}")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir)
                zipf.write(file_path, arc_name)
    
    print(f"✅ تم إنشاء ملف ZIP: {zip_name}")
    return zip_name

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("📦 بناء الحزمة الكاملة - Network Manager Pro")
    print("🎁 Complete Package Builder")
    print("=" * 60)
    
    try:
        # إنشاء الحزمة الكاملة
        package_dir = create_complete_package()
        
        # إنشاء ملف ZIP
        zip_file = create_zip_package(package_dir)
        
        print("\n" + "=" * 60)
        print("🎉 تم إنشاء الحزمة الكاملة بنجاح!")
        print(f"📁 مجلد الحزمة: {package_dir}/")
        print(f"📦 ملف ZIP: {zip_file}")
        print("\n📋 محتويات الحزمة:")
        print("  ✅ NetworkManagerPro.exe - البرنامج الرئيسي")
        print("  ✅ npcap/ - مثبت Npcap")
        print("  ✅ تشغيل_البرنامج.bat - تشغيل سهل")
        print("  ✅ تثبيت_Npcap.bat - تثبيت Npcap")
        print("  ✅ دليل_المستخدم.txt - دليل شامل")
        print("  ✅ logs/ - مجلد السجلات")
        print("  ✅ backups/ - مجلد النسخ الاحتياطية")
        print("\n🚀 طريقة الاستخدام:")
        print("  1. فك ضغط الملف")
        print("  2. تشغيل تثبيت_Npcap.bat")
        print("  3. تشغيل تشغيل_البرنامج.bat")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحزمة: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(exit_code)
