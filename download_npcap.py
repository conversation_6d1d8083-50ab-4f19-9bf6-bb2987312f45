"""
تحميل Npcap تلقائياً
Download Npcap automatically
"""

import os
import sys
import requests
import subprocess
from pathlib import Path

def download_file(url, filename):
    """تحميل ملف من رابط"""
    try:
        print(f"📥 تحميل {filename}...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r📊 التقدم: {percent:.1f}%", end='', flush=True)
        
        print(f"\n✅ تم تحميل {filename} بنجاح")
        return True
        
    except Exception as e:
        print(f"\n❌ فشل في تحميل {filename}: {e}")
        return False

def get_npcap_download_url():
    """الحصول على رابط تحميل Npcap الأحدث"""
    # رابط مباشر لأحدث إصدار من Npcap
    return "https://nmap.org/npcap/dist/npcap-1.79.exe"

def download_npcap():
    """تحميل Npcap"""
    print("🌐 تحميل Npcap...")
    
    # إنشاء مجلد npcap
    npcap_dir = "npcap"
    os.makedirs(npcap_dir, exist_ok=True)
    
    # رابط التحميل
    url = get_npcap_download_url()
    filename = os.path.join(npcap_dir, "npcap-installer.exe")
    
    # تحميل الملف
    if download_file(url, filename):
        print(f"✅ تم تحميل Npcap إلى: {filename}")
        return filename
    else:
        print("❌ فشل في تحميل Npcap")
        return None

def install_npcap(installer_path):
    """تثبيت Npcap"""
    print("🔧 تثبيت Npcap...")
    
    try:
        # تشغيل المثبت بصلاحيات المدير
        subprocess.run([installer_path, "/S"], check=True)
        print("✅ تم تثبيت Npcap بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت Npcap")
        print("💡 جرب تشغيل المثبت يدوياً بصلاحيات المدير")
        return False

def create_npcap_package():
    """إنشاء حزمة Npcap للتوزيع"""
    print("📦 إنشاء حزمة Npcap...")
    
    # تحميل Npcap
    installer_path = download_npcap()
    if not installer_path:
        return False
    
    # إنشاء ملف تعليمات
    instructions = """# تثبيت Npcap

## الطريقة التلقائية:
1. اضغط دبل كليك على npcap-installer.exe
2. اتبع التعليمات
3. **مهم**: تأكد من تحديد خيار "Install Npcap in WinPcap API-compatible Mode"

## الطريقة اليدوية:
1. اذهب إلى: https://nmap.org/npcap/
2. حمل أحدث إصدار
3. ثبته بصلاحيات المدير

## ملاحظة مهمة:
Npcap مطلوب لعمل البرنامج. بدونه لن يعمل فحص الشبكة أو حجب الأجهزة.
"""
    
    with open("npcap/تعليمات_التثبيت.txt", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ تم إنشاء حزمة Npcap")
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("📥 تحميل وإعداد Npcap")
    print("=" * 50)
    
    try:
        # تثبيت requests إذا لم يكن موجود
        try:
            import requests
        except ImportError:
            print("📦 تثبيت مكتبة requests...")
            subprocess.run([sys.executable, "-m", "pip", "install", "requests"], check=True)
            import requests
        
        # إنشاء حزمة Npcap
        if create_npcap_package():
            print("\n🎉 تم تحميل Npcap بنجاح!")
            print("📁 الملفات موجودة في مجلد: npcap/")
            
            choice = input("\nهل تريد تثبيت Npcap الآن؟ (y/n): ").lower()
            if choice == 'y':
                installer_path = "npcap/npcap-installer.exe"
                if os.path.exists(installer_path):
                    install_npcap(installer_path)
                else:
                    print("❌ ملف المثبت غير موجود")
        else:
            print("❌ فشل في تحميل Npcap")
            print("🌐 يمكنك تحميله يدوياً من: https://nmap.org/npcap/")
    
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("🌐 يرجى تحميل Npcap يدوياً من: https://nmap.org/npcap/")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
