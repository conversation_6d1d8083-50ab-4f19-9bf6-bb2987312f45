"""
سكريبت تثبيت المتطلبات - تثبيت جميع الحزم المطلوبة تلقائياً
Requirements Installation Script - Automatically install all required packages
"""

import subprocess
import sys
import os
import ctypes

def check_admin_privileges():
    """فحص صلاحيات المدير"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """تشغيل السكريبت بصلاحيات المدير"""
    try:
        if not check_admin_privileges():
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            sys.exit(0)
    except Exception as e:
        print(f"❌ خطأ في الحصول على صلاحيات المدير: {e}")

def install_package(package_name):
    """تثبيت حزمة Python"""
    try:
        print(f"📦 تثبيت {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ تم تثبيت {package_name} بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت {package_name}")
            print(f"خطأ: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ انتهت مهلة تثبيت {package_name}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت {package_name}: {e}")
        return False

def install_winpcap():
    """تثبيت WinPcap/Npcap المطلوب لـ scapy"""
    print("\n🔧 تثبيت WinPcap/Npcap...")
    print("📝 ملاحظة: scapy يحتاج إلى WinPcap أو Npcap للعمل على Windows")
    print("🌐 يرجى تحميل وتثبيت Npcap من: https://nmap.org/npcap/")
    print("⚠️ تأكد من تحديد خيار 'Install Npcap in WinPcap API-compatible Mode' أثناء التثبيت")
    
    choice = input("\nهل تريد فتح رابط التحميل؟ (y/n): ").lower()
    if choice == 'y':
        try:
            import webbrowser
            webbrowser.open("https://nmap.org/npcap/")
        except:
            print("❌ فشل في فتح المتصفح")

def check_python_version():
    """فحص إصدار Python"""
    version = sys.version_info
    print(f"🐍 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    
    print("✅ إصدار Python مناسب")
    return True

def upgrade_pip():
    """تحديث pip"""
    try:
        print("🔄 تحديث pip...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ تم تحديث pip بنجاح")
        else:
            print("⚠️ تحذير: فشل في تحديث pip")
            
    except Exception as e:
        print(f"⚠️ تحذير: خطأ في تحديث pip: {e}")

def main():
    """الدالة الرئيسية للتثبيت"""
    print("=" * 60)
    print("🚀 مرحباً بك في مثبت مدير الشبكة المتقدم")
    print("📦 Network Manager Pro - Requirements Installer")
    print("=" * 60)
    
    # فحص صلاحيات المدير
    if not check_admin_privileges():
        print("⚠️ يُنصح بتشغيل المثبت بصلاحيات المدير")
        choice = input("هل تريد إعادة التشغيل بصلاحيات المدير؟ (y/n): ").lower()
        if choice == 'y':
            run_as_admin()
            return
    
    # فحص إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # تحديث pip
    upgrade_pip()
    
    # قائمة الحزم المطلوبة
    required_packages = [
        "PyQt5==5.15.9",
        "scapy==2.5.0", 
        "psutil==5.9.5",
        "netifaces==0.11.0"
    ]
    
    print(f"\n📋 سيتم تثبيت {len(required_packages)} حزمة:")
    for package in required_packages:
        print(f"   • {package}")
    
    print("\n🔄 بدء التثبيت...")
    
    # تثبيت الحزم
    success_count = 0
    failed_packages = []
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج التثبيت:")
    print(f"✅ تم تثبيت {success_count} حزمة بنجاح")
    
    if failed_packages:
        print(f"❌ فشل في تثبيت {len(failed_packages)} حزمة:")
        for package in failed_packages:
            print(f"   • {package}")
    
    # تثبيت WinPcap/Npcap
    install_winpcap()
    
    # إنشاء المجلدات المطلوبة
    print("\n📁 إنشاء المجلدات المطلوبة...")
    try:
        os.makedirs("logs", exist_ok=True)
        print("✅ تم إنشاء مجلد logs")
    except Exception as e:
        print(f"❌ خطأ في إنشاء المجلدات: {e}")
    
    # رسالة الانتهاء
    print("\n" + "=" * 60)
    if len(failed_packages) == 0:
        print("🎉 تم التثبيت بنجاح!")
        print("▶️ يمكنك الآن تشغيل البرنامج باستخدام: python gui_main.py")
    else:
        print("⚠️ التثبيت مكتمل مع بعض الأخطاء")
        print("🔧 يرجى تثبيت الحزم الفاشلة يدوياً")
    
    print("\n📝 ملاحظات مهمة:")
    print("• تأكد من تثبيت Npcap من الرابط المذكور أعلاه")
    print("• يجب تشغيل البرنامج بصلاحيات المدير للحصول على جميع الميزات")
    print("• جميع السجلات يتم حفظها في مجلد logs")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
