@echo off
title Build Network Manager Pro

echo.
echo Building Network Manager Pro...
echo ===============================
echo.

:: Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python from: https://python.org
    pause
    exit /b 1
)

echo Python found:
python --version
echo.

:: Install requirements
echo Installing requirements...
pip install --upgrade pip
pip install PyQt5==5.15.9
pip install scapy==2.5.0
pip install psutil==5.9.5
pip install netifaces==0.11.0
pip install pyinstaller

echo.
echo Requirements installed successfully
echo.

:: Build EXE
echo Building EXE file...
pyinstaller --onefile --windowed --name=NetworkManagerPro --uac-admin gui_main.py

if errorlevel 1 (
    echo ERROR: Failed to build EXE
    pause
    exit /b 1
)

echo EXE built successfully
echo.

:: Create distribution folder
echo Creating distribution package...
if exist "NetworkManagerPro_Final" rmdir /s /q "NetworkManagerPro_Final"
mkdir "NetworkManagerPro_Final"
mkdir "NetworkManagerPro_Final\logs"
mkdir "NetworkManagerPro_Final\backups"

:: Copy EXE
copy "dist\NetworkManagerPro.exe" "NetworkManagerPro_Final\"

:: Create launcher
echo @echo off > "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo title Network Manager Pro >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo echo. >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo echo Starting Network Manager Pro... >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo echo =============================== >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo echo. >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo echo Running with Administrator privileges... >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo powershell -Command "Start-Process '%~dp0NetworkManagerPro.exe' -Verb RunAs" >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo echo. >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo echo Program started successfully >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"
echo pause >> "NetworkManagerPro_Final\RUN_AS_ADMIN.bat"

:: Create README
echo Network Manager Pro - Advanced Network Management Tool > "NetworkManagerPro_Final\README.txt"
echo ======================================================= >> "NetworkManagerPro_Final\README.txt"
echo. >> "NetworkManagerPro_Final\README.txt"
echo HOW TO RUN: >> "NetworkManagerPro_Final\README.txt"
echo 1. Double-click on "RUN_AS_ADMIN.bat" >> "NetworkManagerPro_Final\README.txt"
echo 2. OR right-click on "NetworkManagerPro.exe" and select "Run as administrator" >> "NetworkManagerPro_Final\README.txt"
echo. >> "NetworkManagerPro_Final\README.txt"
echo IMPORTANT REQUIREMENTS: >> "NetworkManagerPro_Final\README.txt"
echo - Install Npcap from: https://nmap.org/npcap/ >> "NetworkManagerPro_Final\README.txt"
echo - Make sure to check "Install Npcap in WinPcap API-compatible Mode" >> "NetworkManagerPro_Final\README.txt"
echo - Administrator privileges required >> "NetworkManagerPro_Final\README.txt"
echo. >> "NetworkManagerPro_Final\README.txt"
echo FEATURES: >> "NetworkManagerPro_Final\README.txt"
echo - Network scanning and device discovery >> "NetworkManagerPro_Final\README.txt"
echo - Block devices from internet access >> "NetworkManagerPro_Final\README.txt"
echo - Bandwidth control and speed limiting >> "NetworkManagerPro_Final\README.txt"
echo - Automatic settings save by MAC address >> "NetworkManagerPro_Final\README.txt"
echo - Arabic interface with English support >> "NetworkManagerPro_Final\README.txt"
echo. >> "NetworkManagerPro_Final\README.txt"
echo SECURITY: >> "NetworkManagerPro_Final\README.txt"
echo - All operations are local network only >> "NetworkManagerPro_Final\README.txt"
echo - No data sent outside your network >> "NetworkManagerPro_Final\README.txt"
echo - Use responsibly on your own network only >> "NetworkManagerPro_Final\README.txt"
echo. >> "NetworkManagerPro_Final\README.txt"
echo Version: 1.0.0 >> "NetworkManagerPro_Final\README.txt"
echo Compatible: Windows 10/11 >> "NetworkManagerPro_Final\README.txt"

echo.
echo ===============================
echo BUILD COMPLETED SUCCESSFULLY!
echo ===============================
echo.
echo Program location: NetworkManagerPro_Final\
echo Read README.txt for instructions
echo Don't forget to install Npcap from: https://nmap.org/npcap/
echo.

:: Open results folder
start "" "NetworkManagerPro_Final"

echo Results folder opened
pause
