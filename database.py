"""
وحدة قاعدة البيانات - إدارة البيانات المحلية للأجهزة والإعدادات
Database Module - Local data management for devices and settings
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    """مدير قاعدة البيانات لحفظ إعدادات الأجهزة والسجلات"""
    
    def __init__(self, db_path: str = "network_manager.db"):
        """
        تهيئة قاعدة البيانات
        Initialize database
        
        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء الجداول الأساسية في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول الأجهزة والإعدادات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS devices (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        mac_address TEXT UNIQUE NOT NULL,
                        ip_address TEXT,
                        device_name TEXT,
                        action_type TEXT CHECK(action_type IN ('block', 'limit', 'allow')),
                        download_limit INTEGER DEFAULT 0,
                        upload_limit INTEGER DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول السجلات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        mac_address TEXT,
                        action TEXT NOT NULL,
                        details TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول إعدادات البرنامج
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS settings (
                        key TEXT PRIMARY KEY,
                        value TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                print("✅ تم إنشاء قاعدة البيانات بنجاح")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
    
    def add_device(self, mac_address: str, ip_address: str = "", device_name: str = "", 
                   action_type: str = "allow", download_limit: int = 0, upload_limit: int = 0) -> bool:
        """
        إضافة جهاز جديد أو تحديث جهاز موجود
        Add new device or update existing device
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO devices 
                    (mac_address, ip_address, device_name, action_type, download_limit, upload_limit, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (mac_address, ip_address, device_name, action_type, download_limit, upload_limit, datetime.now()))
                
                conn.commit()
                self.add_log(mac_address, f"تم حفظ إعدادات الجهاز: {action_type}")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في إضافة الجهاز: {e}")
            return False
    
    def get_device_settings(self, mac_address: str) -> Optional[Dict]:
        """الحصول على إعدادات جهاز معين بناءً على MAC Address"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM devices WHERE mac_address = ? AND is_active = 1
                ''', (mac_address,))
                
                row = cursor.fetchone()
                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))
                return None
                
        except Exception as e:
            print(f"❌ خطأ في الحصول على إعدادات الجهاز: {e}")
            return None
    
    def get_all_devices(self) -> List[Dict]:
        """الحصول على جميع الأجهزة المحفوظة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM devices WHERE is_active = 1 ORDER BY updated_at DESC
                ''')
                
                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            print(f"❌ خطأ في الحصول على قائمة الأجهزة: {e}")
            return []
    
    def delete_device(self, mac_address: str) -> bool:
        """حذف جهاز من قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE devices SET is_active = 0, updated_at = ? WHERE mac_address = ?
                ''', (datetime.now(), mac_address))
                
                conn.commit()
                self.add_log(mac_address, "تم حذف الجهاز من الإعدادات")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في حذف الجهاز: {e}")
            return False
    
    def add_log(self, mac_address: str, action: str, details: str = ""):
        """إضافة سجل جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO logs (mac_address, action, details, timestamp)
                    VALUES (?, ?, ?, ?)
                ''', (mac_address, action, details, datetime.now()))
                
                conn.commit()
                
        except Exception as e:
            print(f"❌ خطأ في إضافة السجل: {e}")
    
    def get_logs(self, limit: int = 100) -> List[Dict]:
        """الحصول على السجلات الأخيرة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM logs ORDER BY timestamp DESC LIMIT ?
                ''', (limit,))
                
                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            print(f"❌ خطأ في الحصول على السجلات: {e}")
            return []
    
    def save_setting(self, key: str, value: str):
        """حفظ إعداد عام للبرنامج"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO settings (key, value, updated_at)
                    VALUES (?, ?, ?)
                ''', (key, value, datetime.now()))
                
                conn.commit()
                
        except Exception as e:
            print(f"❌ خطأ في حفظ الإعداد: {e}")
    
    def get_setting(self, key: str, default_value: str = "") -> str:
        """الحصول على إعداد عام للبرنامج"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT value FROM settings WHERE key = ?', (key,))
                row = cursor.fetchone()
                
                return row[0] if row else default_value
                
        except Exception as e:
            print(f"❌ خطأ في الحصول على الإعداد: {e}")
            return default_value
